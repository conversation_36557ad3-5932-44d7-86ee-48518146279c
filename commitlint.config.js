module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // Increase the maximum line length for commit messages
    'header-max-length': [2, 'always', 200], // Increased from default 100 to 200 characters
    'body-max-line-length': [2, 'always', 200], // Also increase body line length if needed

    // Enforce capitalization of the first letter after the colon
    // 'subject-case': [2, 'always', 'sentence-case'], // First letter must be capitalized
  },
};
