import Cookies from 'js-cookie';
import React, { lazy, Suspense } from 'react';
import { useAuth } from 'react-oidc-context';
import { useSelector } from 'react-redux';
import { Route, Routes as RouterRoutes, useLocation } from 'react-router-dom';
import { RootState } from './redux/store';

import {
  ABOUT_GOTRUST,
  ACTIVITY_AUDIT_LOG,
  ACTIVITY_LOG,
  ADD_CONTROL_CATEGORY,
  ASSESSMENT__TEMPLATES,
  ASSESSMENT__TEMPLATES_VIEW,
  ASSESSMENT_ACTION,
  ASSESSMENT_ADD_QUESTION,
  ASSESSMENT_DASHBOARD,
  ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
  ASSESSMENT_MANAGEMENT_TASK_OVERVIEW_AUDIT_LOG,
  ASSESSMENT_REVIEW,
  ASSESSMENT_VIEW,
  ASSESSMENT_VIEW_AUDIT_LOG,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>EACH_DETAILS,
  <PERSON><PERSON>CH_LIST,
  <PERSON><PERSON><PERSON>_MANAGEMENT_DASHBOARD,
  CA<PERSON><PERSON><PERSON>_LOGIN,
  CHANGE_PASSWORD,
  COMMUNITY,
  COMPANY_STRUCTURE,
  COOKIE_CONFIGURATION,
  COOKIE_CONSENT_DOMAIN,
  COOKIE_CONSENT_MANAGEMENT,
  COOKIE_CONSENT_NEW_DASHBOAED,
  COOKIE_DICTIONARY,
  COOKIE_POLICY,
  COOKIE_POLICY_CREATE,
  COOKIE_POLICY_DETAILS,
  CREATE_TICKET,
  CUSOMER_MANAGEMENT,
  CUSTOMER_MANAGEMENT_ADD_CUSTOMER,
  CUSTOMER_MANAGEMENT_EDIT_CUSTOMER,
  CUSTOMER_MANAGEMENT_VIEW_CUSTOMER,
  CUSTOMIZE,
  DATA_CATALOGUE_DASHBOARD,
  DATA_CATALOGUE_UNSTRUCTURED_V0,
  DATA_CATALOGUE_V0,
  DATA_GOVERNANCE_ASSET_DISCOVERY,
  DATA_GOVERNANCE_ASSET_INVENTORY,
  DATA_GOVERNANCE_COMPLIANCE_CENTER,
  DATA_GOVERNANCE_CUSTOM_TAGGING,
  DATA_GOVERNANCE_DATA_CATALOGUE,
  DATA_GOVERNANCE_DATA_CLASSIFICATION,
  DATA_GOVERNANCE_POLICY_CENTER,
  DATA_GOVERNANCE_RISK_ANALYTICS,
  DATA_GOVERNANCE_RISK_ASSESSMENT,
  DATA_INSIGHTS,
  DATA_RETENTION_DASHBOARD,
  DATA_SUBJECT_RIGHTS,
  DOCUMENTS,
  DSR_ADD_QUESTION,
  DSR_ADD_WORKFLOW,
  DSR_ASSIGNEE_VIEW_DETAILS,
  DSR_EDIT_WORKFLOW,
  DSR_EMAIL_TEMPLATES,
  DSR_EMAIL_VER,
  DSR_FORM_BUILDER,
  DSR_FORM_BUILDER_CREATE_FORM,
  DSR_FORM_BUILDER_REVIEW,
  DSR_FORM_BUILDER_VIEW,
  DSR_FORM_REPOSIOTRY,
  DSR_FORM_TRANSLATION,
  DSR_MY_REQUEST,
  DSR_MY_REQUEST_DETAILS,
  DSR_MY_TASK,
  DSR_MY_TASK_VIEW,
  DSR_REQUEST_FORM,
  DSR_RETENTION_SCHEDULE,
  DSR_TASK_OVERVIEW,
  DSR_TASK_OVERVIEW_APPROVED,
  DSR_TASK_OVERVIEW_ARCHIVED,
  DSR_TASK_OVERVIEW_COMPLETED,
  DSR_TASK_OVERVIEW_CREATE,
  DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS,
  DSR_TASK_OVERVIEW_REJECTED,
  DSR_TASK_OVERVIEW_VIEW_APPROVED_REQ,
  DSR_TASK_OVERVIEW_VIEW_ARCHIVED_REQ,
  DSR_TASK_OVERVIEW_VIEW_ASSIGNEE,
  DSR_TASK_OVERVIEW_VIEW_COMPLETED_REQ,
  DSR_TASK_OVERVIEW_VIEW_PENDING_REQ,
  DSR_TASK_OVERVIEW_VIEW_REJECTED_IN_PROGRESS,
  DSR_TASK_OVERVIEW_VIEW_REJECTED_REQ,
  DSR_VIEW_WORKFLOW,
  DSR_WORKFLOW_TABLE,
  EDIT_TICKET_DETAILS,
  FILE_CLASSIFICATION,
  FLOW_DIAGRAM,
  FORGOT_PASSWORD,
  GLOBAL_ADD_WORKFLOW,
  GLOBAL_EDIT_WORKFLOW,
  GLOBAL_VIEW_WORKFLOW,
  GLOBAL_WORKFLOW_TABLE,
  GOTRUST_CONSENT_FORM,
  GOTRUST_CONSENT_FORM_VERIFICATION,
  GOTRUST_COOKIE_POLICY_NOTICE,
  GOTRUST_PREFERENCE_FORM,
  HOME,
  INTERNAL_SERVER_ERROR,
  INVOICE,
  LOGIN,
  LOGIN_WITH_PARAM,
  NOTICE,
  ONBOARDING_QUESTIONS,
  ORGANISATION_DETAILS,
  PII_ANALYSIS,
  PII_HANDBOOK,
  PII_LIST,
  PII_LIST_TABLE,
  PRIVACY_CENTER,
  PRIVACY_OPS_ACTIVITIES_ACTIONS,
  PRIVACY_OPS_ACTIVITIES_DUTIES,
  PRIVACY_OPS_ACTIVITIES_IMPROVEMENTS,
  PRIVACY_OPS_ASSESSMENT_REPO,
  PRIVACY_OPS_ASSESSMENT_REPO_DETAILS,
  PRIVACY_OPS_COMPLIANCE_DASHBOARD,
  PRIVACY_OPS_CONTROL_DETAILS,
  PRIVACY_OPS_CONTROL_HANDBOOK,
  PRIVACY_OPS_DOCUMENT_REPO,
  PRIVACY_OPS_PROCESSING_ACTIVITIES,
  PRIVACY_OPS_PROCESSING_ACTIVITIES_DETAILS,
  PRIVACY_OPS_REGULATIONS,
  PRIVACY_OPS_REGULATIONS_DETAILS,
  PRIVACY_OPS_RISK_DASHBOARD,
  PRIVACY_OPS_RISK_REGISTER,
  PRIVACY_OPS_RISK_REGISTER_AUDIT,
  PRIVACY_OPS_RISK_REGISTER_DETAILS,
  PRIVACY_OPS_VRM_REPO,
  PRIVACY_OPS_VRM_REPO_DETAILS_LISTING,
  PRIVACY_OPS_VRM_REPO_VIEW_DETAIL,
  PRIVACY_POLICY_CREATE_POLICY,
  PRIVACY_POLICY_CREATE_POLICY_TEMPLATE,
  PRIVACY_POLICY_MANAGEMENT,
  PROFILE,
  RECORDED_CONSENTS,
  RECOVERY,
  RETENTION_RULE_DETAILS,
  RETENTION_RULE_DETAILS_VIEW,
  RETENTION_RULE_LIST,
  ROLE_MANAGEMENT,
  ROLE_MANAGEMENT_ADD_ROLE,
  ROLE_MANAGEMENT_EDIT_ROLE,
  ROLE_MANAGEMENT_EDIT_ROLE_DETAILS,
  ROLE_MANAGEMENT_ROLE_DETAILS,
  ROPA_ADD_QUESTION,
  ROPA_BASIC_INFORMATION,
  ROPA_DASHBOARD,
  ROPA_REGISTER,
  ROPA_RIVIEW,
  ROPA_VIEW,
  ROPA_VIEW_AUDIT_LOG,
  SECURITY_OPS_AISPM,
  SECURITY_OPS_ASSET_DISCOVERY,
  SECURITY_OPS_CSPM,
  SECURITY_OPS_DSPM,
  SELECTED_VIEW_GROUP_DETAILS,
  SET_PASSWORD,
  SIGNUP,
  STRUCTURED_DATA_CATALOGUE,
  STRUCTURED_INGESTION,
  STRUCTURED_SERVICES,
  SUPPORT,
  UCF,
  UCF_ACTIONS,
  UCF_IMPROVENENT,
  UCF_ONBOARDING,
  UCM_ADD_COLLECTION_TEMPLATE,
  UCM_ADD_COLLECTION_TEMPLATE_POC,
  UCM_COLLECTION_TEMPLATE,
  UCM_CONSENT_PURPOSE,
  UCM_FORM,
  UCM_FORM_CREATE,
  UCM_PII,
  UCM_PRIVACY_NOTICE,
  UCM_PRIVACY_NOTICE_DETAILS,
  UCM_PROCESSING_CATEGORY,
  UCM_PROCESSING_PURPOSE,
  UCM_SOURCE_CONSENT_UPLOAD,
  UCM_SUBJECT_CONSENT_LIST,
  UCM_SUBJECT_CONSENT_LIST_DETAILS,
  UCM_SUBJECT_CONSENT_MANAGER,
  UCM_SUBJECT_CONSENT_MANAGER_LIST_DETAILS,
  UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE,
  UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE_DETAILS,
  UCM_SUBJECT_CONSENT_TYPES,
  UCM_SUBJECT_CONSENT_TYPES_DETAILS,
  UCM_TEMPLATES,
  UNAUTHORIZED,
  UNIVERSAL_CONSENT_ADD_PREFERENCECENTRE_CENTER,
  UNIVERSAL_CONSENT_CONSENT_PURPOSE,
  UNIVERSAL_CONSENT_CUSTOME_PARAMETERS,
  UNIVERSAL_CONSENT_MANAGEMENT_CONSENTUPLOADS,
  UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
  UNIVERSAL_CONSENT_MANAGEMENT_FORMCENTRE,
  UNIVERSAL_CONSENT_MANAGEMENT_NEW_DASHBOARD,
  UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE,
  UNIVERSAL_CONSENT_UPDATE_PREFERENCECENTRE_CENTER,
  UNSTRUCTURED_DATA_CATALOGUE,
  UNSTRUCTURED_DATA_MAPPING,
  UNSTRUCTURED_INGESTION,
  UNSTRUCTURED_SERVICES,
  USER_MANAGEMENT,
  VENDOR_RISK_MANAGEMENT_DASHBOARD,
  VERIFY_EMAIL,
  VIDEOS,
  VIEW_COOKIE_POLICY,
  VIEW_DETAIL_BLOGS,
  VIEW_INTERNAL_ASSESSMENT_QUESTIONS,
  VIEW_TICKET_DETAILS,
  VIEW_UCM_PRIVACY_NOTICE_DETAILS,
  VISUALIZATION,
  VRM_ASSESSMENT_ACTION,
  VRM_ASSESSMENT_ADD_QUESTION,
  VRM_ASSESSMENT_MITIGATION,
  VRM_ASSESSMENT_REVIEW,
  VRM_ASSESSMENT_TEMPLATES,
  VRM_TASK_OVERVIEW,
  VRM_TASK_OVERVIEW_AUDIT_LOG,
  VRM_TASK_OVERVIEW_CREATE,
  VRM_TEMPLATES_VIEW,
  VRM_VENDOR_DETAILS,
  VRM_VIEW_INTERNAL_ASSESSMENT,
  VRM_VIEW_INTERNAL_ASSESSMENT_AUDIT_LOG,
  VRM_VIEW_VENDOR_ASSESSMENT,
  VRM_VIEW_VENDOR_ASSESSMENT_AUDIT_LOG,
} from './utils/routeConstant';

const Blogs = lazy(() => import('./components/AboutUs/Blogs/Blogs'));
const ViewDetailBlog = lazy(() => import('./components/AboutUs/Blogs/ViewDetailBlog'));
const Community = lazy(() => import('./components/AboutUs/Community/Community'));
const Documents = lazy(() => import('./components/AboutUs/HowToUse/Documents/Documents'));
const Videos = lazy(() => import('./components/AboutUs/HowToUse/Videos/Videos'));
const ActivityLog = lazy(() => import('./components/ActivityLog/ActivityLog'));
const UnstructuredDataInventory = lazy(
  () => import('./components/ActivityLog/unstructured-data-inventory')
);
const UnstructuredDataMapping = lazy(
  () => import('./components/ActivityLog/UnstructuredDataMapping')
);
const AddAssessmentQuestion = lazy(
  () => import('./components/Assessments/Controls/AddQuestion/AddQuestion')
);
// const AssessmentDashboard = lazy(
//   () => import('./components/Assessments/Dashboard/AssessmentDashboard')
// );
const AssessmentDashboard = lazy(
  () => import('./components/Assessments/Dashboard/NewAssessmentDashboard')
);
const AssessmentTemplate = lazy(() => import('./components/Assessments/Lab/assessment-template'));
const AssessmentTemplateView = lazy(
  () => import('./components/Assessments/Lab/assessment-template-view')
);
const ReviewAssessment = lazy(() => import('./components/Assessments/Review/ReviewAssessment'));
const RiskAssessment = lazy(() => import('./components/Assessments/RiskAssessment'));
const AssessmentView = lazy(() => import('./components/Assessments/View/AssessmentView'));
const AboutUs = lazy(() => import('./components/common/AboutUs'));
const ErrorPage = lazy(() => import('./components/common/ErrorPage'));
const InternalServer = lazy(() => import('./components/common/InternalServer'));
const OnboardingWrapper = lazy(() => import('./components/common/MainWrapper/OnboardingWrapper'));
const NotFound = lazy(() => import('./components/common/NotFound'));
const AuthorizationWrapper = lazy(
  () => import('./components/common/OnboardingTemplate/AuthorizationWrapper')
);
const OverlayLoader = lazy(() => import('./components/common/OverlayLoader'));
const Unauthorized = lazy(() => import('./components/common/Unauthorized'));
const CompanyStructure = lazy(() => import('./components/Company Structure/CompanyStructure'));
const ViewGroupDetails = lazy(() => import('./components/Company Structure/ViewGroupDetails'));
const AddEditViewCustomer = lazy(
  () => import('./components/CustomerManagementv2/AddEditViewCustomer')
);
const CustomerManagementList = lazy(
  () => import('./components/CustomerManagementv2/Customermanagement')
);
const DsrOtpVerification = lazy(
  () => import('./components/DSR/Lab/FormBuilder/create-form/dsr-otp-verification')
);
const RequestForm = lazy(
  () => import('./components/DSR/Lab/FormBuilder/create-form/dsr-public-form')
);
const EmaiilVer = lazy(
  () => import('./components/DSR/Lab/FormBuilder/create-form/email-verification')
);
const ThankYouPage = lazy(
  () => import('./components/DSR/Lab/FormBuilder/create-form/thank-you-page')
);
const DSRAddQuestion = lazy(
  () => import('./components/DSR/Lab/FormBuilder/DSRAddQuestion/DSRAddQuestion')
);
const FormTranslation = lazy(
  () => import('./components/DSR/Lab/FormBuilder/create-form/form-translation')
);
const InvoiceTableLayout = lazy(() => import('./components/Invoice/InvoiceTableLayout'));
const Home = lazy(() => import('./components/Layout/Home'));
const Questions = lazy(() => import('./components/Onboarding/Questions'));
const CreatePolicyTemplate = lazy(
  () => import('./components/Policy/Template/CreatePolicyTemplate')
);
const Policy = lazy(() => import('./components/PolicyManagement/Main/Policy'));
const CreatePolicy = lazy(
  () => import('./components/PrivacyAndDataProtection/CreatePolicy/CreatePolicy')
);
const ChangePassword = lazy(() => import('./components/Profile/ChangePassword'));
const Profile = lazy(() => import('./components/Profile/Profile'));
const AddRole = lazy(() => import('./components/roleManagement/addRole'));
const EditRole = lazy(() => import('./components/roleManagement/editRole'));
const EditRoleDetails = lazy(() => import('./components/roleManagement/editRoleDetails'));
const RoleManagement = lazy(() => import('./components/roleManagement/roleManagement'));
// const DashboardRopa = lazy(() => import('./components/Ropa/Dashboard/DashboardRopa'));
const DashboardRopa = lazy(() => import('./components/Ropa/Dashboard/ropa'));
const AddQuestion = lazy(() => import('./components/Ropa/Questionnaire/AddQuestion/AddQuestion'));
const RopaBasicsInformation = lazy(
  () => import('./components/Ropa/Questionnaire/RopaBasicsInformation')
);
const RopaRegister = lazy(() => import('./components/Ropa/Questionnaire/RopaRegister'));
const RopaReview = lazy(() => import('./components/Ropa/Questionnaire/RopaReview'));
const PrivateRoutes = lazy(() => import('./components/ScopeRoute/ProtectedRoute'));
const PublicRoutes = lazy(() => import('./components/ScopeRoute/PublicRoute'));
const OrgDetails = lazy(() => import('./components/Signup/OrgDetails'));
const EditTicketDetails = lazy(() => import('./components/Support/EditTicketDetails'));
const SupportEmployeeDashboard = lazy(
  () => import('./components/Support/Employee/SupportEmployee')
);
const CreateTicket = lazy(() => import('./components/Support/SubSuport/CreateTicket/CreateTicket'));
const Support = lazy(() => import('./components/Support/Support'));
const ViewTicket = lazy(() => import('./components/Support/ViewTicket'));
const CollectionTemplates = lazy(
  () => import('./components/UniversalConsentMangement/PreferenceCentre/collection-templates')
);
const UserManagement = lazy(() => import('./components/UserManagement/UserManagement'));
const AddVrmAssessmentQuestion = lazy(
  () =>
    import('./components/VenedorRiskManagement/Assessment/Controls/AddQuestion/vrm-add-question')
);
const VrmAssessment = lazy(
  () => import('./components/VenedorRiskManagement/Assessment/Controls/vrm-assessment')
);
const ReviewVrmAssessment = lazy(
  () => import('./components/VenedorRiskManagement/Assessment/Review/review-vrm-assessment')
);
const CreateVendor = lazy(
  () => import('./components/VenedorRiskManagement/CreateVRM/create-vendor')
);
const InternalAssessmentView = lazy(
  () =>
    import(
      './components/VenedorRiskManagement/CreateVRM/InternalAssessment/View/internal-assessment-view'
    )
);
const ViewVrmAssessment = lazy(
  () =>
    import(
      './components/VenedorRiskManagement/CreateVRM/InternalAssessment/View/internal-assessment-view-question'
    )
);
const VendorAssessment = lazy(
  () => import('./components/VenedorRiskManagement/CreateVRM/VendorAssessment/vendor-assessment')
);
const VendorAssessmentView = lazy(
  () =>
    import(
      './components/VenedorRiskManagement/CreateVRM/VendorAssessment/View/vendor-assessment-view'
    )
);
const VrmAssessmentTemplateView = lazy(
  () => import('./components/VenedorRiskManagement/Lab/Template/vrm-assessment-template-view')
);
const VrmMitigation = lazy(
  () => import('./components/VenedorRiskManagement/Mitigation/vrm-mitigation')
);

const AssessmentManagement = lazy(() => import('./pages/AssessmentManagement'));
const UCMForm = lazy(() => import('./pages/ConsentFrom'));
const ConsentFormVerificaton = lazy(() => import('./pages/ConsentFrom/consent-form-verification'));
const CookieConsentManagement = lazy(() => import('./pages/CookieConsentManagement'));
const Customization = lazy(() => import('./pages/Customization'));
const Visualization = lazy(() => import('./pages/Visualization'));
const DataSubjectRights = lazy(() => import('./pages/DataSubjectRights'));
const GlobalWorkflow = lazy(() => import('./pages/GlobalWorkflow'));
const Notice = lazy(() => import('./pages/Notice'));
const PreferenceForm = lazy(() => import('./pages/PreferenceForm'));
const PrivacyCentreIndex = lazy(() => import('./pages/PrivacyCentre'));
const PrivacyOperations = lazy(() => import('./pages/PrivacyOps'));
const RopaTaskOverview = lazy(() => import('./pages/RopaTaskOverview'));
const UniversalConsentManagement = lazy(() => import('./pages/UniversalConsentManagement'));
const UniversalControlSystem = lazy(() => import('./pages/UniversalControlFramework'));
const VenedorRiskManagement = lazy(() => import('./pages/VenedorRiskManagement'));

// Security Ops

const SecurityOps = lazy(() => import('./pages/SecurityOps/index'));

import { ViewCookiePolicy } from './components/CookieConsentManagement/cookie-policy/ViewCookiePolicy';
import DataGovernanceAnalytics from './components/DataGovernance/Analytics/Analytics';
import AssetDiscoveryDashboard from './components/DataGovernance/AssetDiscovery/AssetDiscoveryDashboard';
import AssetInventory from './components/DataGovernance/AssetInventory/AssetInventory';
import ComplianceCenter from './components/DataGovernance/ComplianceCenter/ComplianceCenter';
import CustomTagging from './components/DataGovernance/CustomTagging/CustomTagging';
import DataGovernanceDataCatalog from './components/DataGovernance/DataCatalogue/DataCatalogue';
import PolicyManagement from './components/DataGovernance/PolicyManagement/PolicyManagementPage';
import DataGovernanceRiskAssessment from './components/DataGovernance/RiskAssessment/RiskAssessment';
import { RiskRegisterStepper } from './components/PrivacyOps/RiskRegister/RiskRegisterStepperForm/RiskRegisterSteper';
import RoleDetails from './components/roleManagement/roleDetails';
import { ClassificationResultsPage } from './components/SecurityOps/DSPM/ClassificationResultsPage';
import { BreachManagement } from './pages/BreachManagement';
import { CookiePolicyNotice } from './pages/CookiePolicyNotice';
import { DataDiscovery } from './pages/DataDiscovery';
import { DataRetention } from './pages/DataRetention';

type LazyElementProps = {
  Component: React.ComponentType<any>;
  pathName?: string;
};

const LazyElement: React.FC<LazyElementProps> = ({ Component, pathName }) => (
  <Suspense fallback={<OverlayLoader />}>
    <Component pathName={pathName} />
  </Suspense>
);

const AppRoutes: React.FC = () => {
  const appData = useSelector((state: RootState) => state);
  const loginData = appData?.auth?.login?.login_details;

  const currentCustomerType = loginData?.customer_type;
  const role = loginData?.role;
  const isManager = role === 'Data Protection Officer' || role === 'God Admin';

  return (
    <RouterRoutes>
      <Route
        path={HOME}
        element={
          <PrivateRoutes>
            <LazyElement Component={Home} />
          </PrivateRoutes>
        }
      />
      <Route
        path="/"
        element={
          <PrivateRoutes>
            <LazyElement Component={Home} />
          </PrivateRoutes>
        }
      />
      <Route path={UNAUTHORIZED} element={<LazyElement Component={Unauthorized} />} />
      <Route path={INTERNAL_SERVER_ERROR} element={<LazyElement Component={InternalServer} />} />
      <Route
        path={SIGNUP}
        element={
          <PublicRoutes>
            <LazyElement Component={AuthorizationWrapper} pathName={SIGNUP} />
          </PublicRoutes>
        }
      />
      <Route
        path={CANNOT_LOGIN}
        element={
          <PrivateRoutes>
            <LazyElement Component={AuthorizationWrapper} pathName={CANNOT_LOGIN} />
          </PrivateRoutes>
        }
      />
      <Route
        path={RECOVERY}
        element={
          <PrivateRoutes>
            <LazyElement Component={AuthorizationWrapper} pathName={RECOVERY} />
          </PrivateRoutes>
        }
      />
      <Route
        path={FORGOT_PASSWORD}
        element={
          <PrivateRoutes>
            <LazyElement Component={AuthorizationWrapper} pathName={FORGOT_PASSWORD} />
          </PrivateRoutes>
        }
      />
      <Route
        path={CUSTOMIZE}
        element={
          <PrivateRoutes>
            <LazyElement Component={Customization} pathName={CUSTOMIZE} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VISUALIZATION}
        element={
          <PrivateRoutes>
            <LazyElement Component={Visualization} pathName={VISUALIZATION} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ORGANISATION_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={OrgDetails} pathName={ORGANISATION_DETAILS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VERIFY_EMAIL}
        element={
          <PrivateRoutes>
            <LazyElement Component={AuthorizationWrapper} pathName={VERIFY_EMAIL} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ONBOARDING_QUESTIONS}
        element={
          <OnboardingWrapper>
            <Questions />
          </OnboardingWrapper>
        }
      />
      <Route
        path={SET_PASSWORD}
        element={
          <PrivateRoutes>
            <LazyElement Component={AuthorizationWrapper} pathName={SET_PASSWORD} />
          </PrivateRoutes>
        }
      />
      <Route
        path={SECURITY_OPS_DSPM}
        element={
          <PrivateRoutes>
            <LazyElement Component={SecurityOps} pathName={SECURITY_OPS_DSPM} />
          </PrivateRoutes>
        }
      />
      <Route
        path={SECURITY_OPS_ASSET_DISCOVERY}
        element={
          <PrivateRoutes>
            <LazyElement Component={SecurityOps} pathName={SECURITY_OPS_ASSET_DISCOVERY} />
          </PrivateRoutes>
        }
      />
      <Route
        path={SECURITY_OPS_CSPM}
        element={
          <PrivateRoutes>
            <LazyElement Component={SecurityOps} pathName={SECURITY_OPS_CSPM} />
          </PrivateRoutes>
        }
      />
      <Route
        path={SECURITY_OPS_AISPM}
        element={
          <PrivateRoutes>
            <LazyElement Component={SecurityOps} pathName={SECURITY_OPS_AISPM} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_SUBJECT_RIGHTS}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={DATA_SUBJECT_RIGHTS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_WORKFLOW_TABLE}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={DSR_WORKFLOW_TABLE} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_ADD_WORKFLOW}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={DSR_ADD_WORKFLOW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_EDIT_WORKFLOW}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={DSR_EDIT_WORKFLOW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_VIEW_WORKFLOW}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={DSR_VIEW_WORKFLOW} />
          </PrivateRoutes>
        }
      />
      {/* GLOBAL WORKFLOW */}
      <Route
        path={GLOBAL_WORKFLOW_TABLE}
        element={
          <PrivateRoutes>
            <LazyElement Component={GlobalWorkflow} pathName={GLOBAL_WORKFLOW_TABLE} />
          </PrivateRoutes>
        }
      />
      <Route
        path={GLOBAL_ADD_WORKFLOW}
        element={
          <PrivateRoutes>
            <LazyElement Component={GlobalWorkflow} pathName={GLOBAL_ADD_WORKFLOW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={GLOBAL_EDIT_WORKFLOW}
        element={
          <PrivateRoutes>
            <LazyElement Component={GlobalWorkflow} pathName={GLOBAL_EDIT_WORKFLOW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={GLOBAL_VIEW_WORKFLOW}
        element={
          <PrivateRoutes>
            <LazyElement Component={GlobalWorkflow} pathName={GLOBAL_VIEW_WORKFLOW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_POLICY_CREATE_POLICY}
        element={
          <PrivateRoutes>
            <LazyElement Component={CreatePolicy} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_COMPLIANCE_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={PrivacyOperations}
              pathName={PRIVACY_OPS_COMPLIANCE_DASHBOARD}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_POLICY_CREATE_POLICY_TEMPLATE}
        element={
          <PrivateRoutes>
            <LazyElement Component={CreatePolicyTemplate} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ASSESSMENT_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement Component={AssessmentDashboard} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ASSESSMENT_MANAGEMENT_TASK_OVERVIEW}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={AssessmentManagement}
              pathName={ASSESSMENT_MANAGEMENT_TASK_OVERVIEW}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={ASSESSMENT_MANAGEMENT_TASK_OVERVIEW_AUDIT_LOG}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={AssessmentManagement}
              pathName={ASSESSMENT_MANAGEMENT_TASK_OVERVIEW_AUDIT_LOG}
            />
          </PrivateRoutes>
        }
      />
      {/* <Route
        path={`${RISK_ASSESSMENT.slice(0, -1)}${
          currentRiskAssessment.key
        }/basics-information`}
        element={
          <PrivateRoutes>
            <AssessmentBasicsInformation />
          </PrivateRoutes>
        }
      /> */}
      <Route
        path={`${ASSESSMENT_VIEW}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={AssessmentView} pathName={`${ASSESSMENT_VIEW}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${ASSESSMENT_VIEW_AUDIT_LOG}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={AssessmentView} pathName={`${ASSESSMENT_VIEW_AUDIT_LOG}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ACTIVITY_LOG}
        element={
          <PrivateRoutes>
            <LazyElement Component={ActivityLog} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ACTIVITY_AUDIT_LOG}
        element={
          <PrivateRoutes>
            <LazyElement Component={ActivityLog} />
          </PrivateRoutes>
        }
      />
      {/* <Route
        path={`${ASSESSMENT_DASHBOARD}/*`}
        element={
          <PrivateRoutes>
            <AssessmentView
              pathName={`${ASSESSMENT_DASHBOARD}/${
                currentRiskAssessmentKey
              }/view/audit-log`}
            />
          </PrivateRoutes>
        }
      /> */}
      <Route
        path={`${ASSESSMENT_ACTION}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={RiskAssessment} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${ASSESSMENT_REVIEW}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={ReviewAssessment} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${ASSESSMENT_ADD_QUESTION}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={AddAssessmentQuestion} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${ASSESSMENT__TEMPLATES}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={AssessmentTemplate} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${ASSESSMENT__TEMPLATES_VIEW}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={AssessmentTemplateView} />
          </PrivateRoutes>
        }
      />
      <Route
        path={USER_MANAGEMENT}
        element={
          <PrivateRoutes>
            <LazyElement Component={UserManagement} />
          </PrivateRoutes>
        }
      />
      <Route
        path={INVOICE}
        element={
          <PrivateRoutes>
            <LazyElement Component={InvoiceTableLayout} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROPA_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement Component={DashboardRopa} />
            {/* <RopaDashboardV2 /> */}
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_POLICY_MANAGEMENT}
        element={
          <PrivateRoutes>
            {/* <PrivacyPolicy /> */}
            <LazyElement Component={Policy} />
          </PrivateRoutes>
        }
      />
      {/* <Route
        path={SCANLINK}
        element={
          <PrivateRoutes>
            <ScanLink />
          </PrivateRoutes>
        }
      /> */}
      {/* DD routes  */}
      <Route
        path={STRUCTURED_DATA_CATALOGUE}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={STRUCTURED_DATA_CATALOGUE} />
          </PrivateRoutes>
        }
      ></Route>
      <Route
        path={STRUCTURED_SERVICES}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={STRUCTURED_SERVICES} />
          </PrivateRoutes>
        }
      ></Route>
      <Route
        path={STRUCTURED_SERVICES}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={STRUCTURED_SERVICES} />
          </PrivateRoutes>
        }
      ></Route>
      <Route
        path={DATA_CATALOGUE_V0}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={DATA_CATALOGUE_V0} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_CATALOGUE_UNSTRUCTURED_V0}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={DATA_CATALOGUE_UNSTRUCTURED_V0} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNSTRUCTURED_DATA_CATALOGUE}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={UNSTRUCTURED_DATA_CATALOGUE} />
          </PrivateRoutes>
        }
      ></Route>
      <Route
        path={UNSTRUCTURED_SERVICES}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={UNSTRUCTURED_SERVICES} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNSTRUCTURED_INGESTION}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={UNSTRUCTURED_INGESTION} />
          </PrivateRoutes>
        }
      />
      <Route
        path={STRUCTURED_INGESTION}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={STRUCTURED_INGESTION} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_CATALOGUE_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={DATA_CATALOGUE_DASHBOARD} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PII_LIST_TABLE}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={PII_LIST_TABLE} />
          </PrivateRoutes>
        }
      />
      <Route
        path={FLOW_DIAGRAM}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={FLOW_DIAGRAM} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_INSIGHTS}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={DATA_INSIGHTS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PII_HANDBOOK}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={PII_HANDBOOK} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PII_ANALYSIS}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={PII_ANALYSIS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={FILE_CLASSIFICATION}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataDiscovery} pathName={FILE_CLASSIFICATION} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ABOUT_GOTRUST}
        element={
          <PrivateRoutes>
            <LazyElement Component={AboutUs} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DOCUMENTS}
        element={
          <PrivateRoutes>
            <LazyElement Component={Documents} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VIDEOS}
        element={
          <PrivateRoutes>
            <LazyElement Component={Videos} />
          </PrivateRoutes>
        }
      />
      <Route
        path={BLOGS}
        element={
          <PrivateRoutes>
            <LazyElement Component={Blogs} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VIEW_DETAIL_BLOGS}
        element={
          <PrivateRoutes>
            <LazyElement Component={ViewDetailBlog} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COMMUNITY}
        element={
          <PrivateRoutes>
            <LazyElement Component={Community} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PROFILE}
        element={
          <PrivateRoutes>
            <LazyElement Component={Profile} />
          </PrivateRoutes>
        }
      />
      <Route
        path={CHANGE_PASSWORD}
        element={
          <PrivateRoutes>
            <LazyElement Component={ChangePassword} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROLE_MANAGEMENT}
        element={
          <PrivateRoutes>
            <LazyElement Component={RoleManagement} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROLE_MANAGEMENT_ROLE_DETAILS}
        element={
          <PrivateRoutes>
            <RoleDetails />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROLE_MANAGEMENT_ADD_ROLE}
        element={
          <PrivateRoutes>
            <LazyElement Component={AddRole} />
          </PrivateRoutes>
        }
      />
      <Route
        path={EDIT_TICKET_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={EditTicketDetails} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROLE_MANAGEMENT_EDIT_ROLE}
        element={
          <PrivateRoutes>
            <LazyElement Component={EditRole} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROLE_MANAGEMENT_EDIT_ROLE_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={EditRoleDetails} />
          </PrivateRoutes>
        }
      />
      <Route
        path={SUPPORT}
        element={
          <PrivateRoutes>
            {isManager ? (
              <LazyElement Component={Support} />
            ) : (
              <LazyElement Component={SupportEmployeeDashboard} />
            )}
          </PrivateRoutes>
        }
      />
      {/* <Route
        path={SUPPORT_DASBOARD}
        element={
          <PrivateRoutes>
            {isManager ? <SupportDashboard /> : <SupportEmployeeDashboard />}
          </PrivateRoutes>
        }
      /> */}
      <Route
        path={CREATE_TICKET}
        element={
          <PrivateRoutes>
            <LazyElement Component={CreateTicket} />
          </PrivateRoutes>
        }
      />
      {/* <Route
        path={ALL_TICKET}
        element={
          <PrivateRoutes>{isManager ? <AllTicket /> : <SupportEmployeeDashboard />}</PrivateRoutes>
        }
      /> */}
      {/* <Route
        path={CLOSE_TICKET}
        element={
          <PrivateRoutes>
            {isManager ? <CloseTicket /> : <SupportEmployeeDashboard />}
          </PrivateRoutes>
        }
      /> */}
      {/* <Route
        path={OPEN_TICKET}
        element={
          <PrivateRoutes>{isManager ? <OpenTicket /> : <SupportEmployeeDashboard />}</PrivateRoutes>
        }
      /> */}
      <Route
        path={CUSOMER_MANAGEMENT}
        element={
          <PrivateRoutes>
            <LazyElement Component={CustomerManagementList} />
          </PrivateRoutes>
        }
      />
      <Route
        path={CUSTOMER_MANAGEMENT_ADD_CUSTOMER}
        element={
          <PrivateRoutes>
            <LazyElement Component={AddEditViewCustomer} />
          </PrivateRoutes>
        }
      />
      <Route
        path={CUSTOMER_MANAGEMENT_EDIT_CUSTOMER}
        element={
          <PrivateRoutes>
            <LazyElement Component={AddEditViewCustomer} />
          </PrivateRoutes>
        }
      />
      <Route
        path={CUSTOMER_MANAGEMENT_VIEW_CUSTOMER}
        element={
          <PrivateRoutes>
            <LazyElement Component={AddEditViewCustomer} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ONBOARDING_QUESTIONS}
        element={
          <PrivateRoutes>
            <LazyElement Component={Questions} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COMPANY_STRUCTURE}
        element={
          <PrivateRoutes>
            <LazyElement Component={CompanyStructure} />
          </PrivateRoutes>
        }
      />
      <Route
        path={SELECTED_VIEW_GROUP_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={ViewGroupDetails} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VIEW_TICKET_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={ViewTicket} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ACTIVITY_LOG}
        element={
          <PrivateRoutes>
            <LazyElement Component={ViewTicket} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ACTIVITY_AUDIT_LOG}
        element={
          <PrivateRoutes>
            <LazyElement Component={ViewTicket} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROPA_BASIC_INFORMATION}
        element={
          <PrivateRoutes>
            <LazyElement Component={RopaBasicsInformation} pathName={ROPA_BASIC_INFORMATION} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROPA_REGISTER}
        element={
          <PrivateRoutes>
            <LazyElement Component={RopaRegister} pathName={ROPA_REGISTER} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROPA_ADD_QUESTION}
        element={
          <PrivateRoutes>
            <LazyElement Component={AddQuestion} pathName={ROPA_ADD_QUESTION} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROPA_RIVIEW}
        element={
          <PrivateRoutes>
            <LazyElement Component={RopaReview} pathName={ROPA_RIVIEW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VRM_TASK_OVERVIEW}
        element={
          <PrivateRoutes>
            {currentCustomerType === 'CUSTOMER' ? (
              <LazyElement Component={VenedorRiskManagement} pathName={VRM_TASK_OVERVIEW} />
            ) : (
              <LazyElement Component={VendorAssessment} />
            )}
          </PrivateRoutes>
        }
      />
      <Route
        path={VRM_TASK_OVERVIEW_AUDIT_LOG}
        element={
          <PrivateRoutes>
            <LazyElement Component={VenedorRiskManagement} pathName={VRM_TASK_OVERVIEW_AUDIT_LOG} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VRM_VENDOR_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={VenedorRiskManagement} pathName={VRM_VENDOR_DETAILS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VENDOR_RISK_MANAGEMENT_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={VenedorRiskManagement}
              pathName={VENDOR_RISK_MANAGEMENT_DASHBOARD}
            />
          </PrivateRoutes>
        }
      />
      {/* <Route
        path={VENDOR_RISK_MANAGEMENT_REVIEW}
        element={
          <PrivateRoutes>
            <VenedorRiskManagement pathName={VENDOR_RISK_MANAGEMENT_DASHBOARD} />
          </PrivateRoutes>
        }
      /> */}
      <Route
        path={VRM_TASK_OVERVIEW_CREATE}
        element={
          <PrivateRoutes>
            <LazyElement Component={CreateVendor} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${VRM_VIEW_INTERNAL_ASSESSMENT}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={InternalAssessmentView}
              pathName={`${VRM_VIEW_INTERNAL_ASSESSMENT}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${VRM_VIEW_INTERNAL_ASSESSMENT_AUDIT_LOG}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={InternalAssessmentView}
              pathName={`${VRM_VIEW_INTERNAL_ASSESSMENT_AUDIT_LOG}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${VRM_VIEW_VENDOR_ASSESSMENT}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={VendorAssessmentView}
              pathName={`${VRM_VIEW_VENDOR_ASSESSMENT}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${VRM_VIEW_VENDOR_ASSESSMENT_AUDIT_LOG}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={VendorAssessmentView}
              pathName={`${VRM_VIEW_VENDOR_ASSESSMENT_AUDIT_LOG}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${VRM_ASSESSMENT_ACTION}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={VrmAssessment} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${VRM_ASSESSMENT_ADD_QUESTION}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={AddVrmAssessmentQuestion} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${VRM_ASSESSMENT_REVIEW}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={ReviewVrmAssessment} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${VRM_ASSESSMENT_MITIGATION}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={VrmMitigation} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VRM_ASSESSMENT_TEMPLATES}
        element={
          <PrivateRoutes>
            <LazyElement Component={VenedorRiskManagement} pathName={VRM_ASSESSMENT_TEMPLATES} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VIEW_INTERNAL_ASSESSMENT_QUESTIONS}
        element={
          <PrivateRoutes>
            <ViewVrmAssessment />
          </PrivateRoutes>
        }
      />
      <Route
        path={VRM_TEMPLATES_VIEW}
        element={
          <PrivateRoutes>
            <VrmAssessmentTemplateView />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_RISK_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_RISK_DASHBOARD} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_DOCUMENT_REPO}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_DOCUMENT_REPO} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_CONTROL_HANDBOOK}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_CONTROL_HANDBOOK} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_PROCESSING_ACTIVITIES}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={PrivacyOperations}
              pathName={PRIVACY_OPS_PROCESSING_ACTIVITIES}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_PROCESSING_ACTIVITIES_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={PrivacyOperations}
              pathName={PRIVACY_OPS_PROCESSING_ACTIVITIES_DETAILS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_ASSESSMENT_REPO_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={PrivacyOperations}
              pathName={PRIVACY_OPS_ASSESSMENT_REPO_DETAILS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_VRM_REPO}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_VRM_REPO} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_VRM_REPO_VIEW_DETAIL}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={PrivacyOperations}
              pathName={PRIVACY_OPS_VRM_REPO_VIEW_DETAIL}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_VRM_REPO_DETAILS_LISTING}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={PrivacyOperations}
              pathName={PRIVACY_OPS_VRM_REPO_DETAILS_LISTING}
            />
          </PrivateRoutes>
        }
      />
      PRIVACY_OPS_CONTROL_DETAILS
      <Route
        path={PRIVACY_OPS_CONTROL_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_CONTROL_DETAILS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_ASSESSMENT_REPO}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_ASSESSMENT_REPO} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_REGULATIONS}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_REGULATIONS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ADD_CONTROL_CATEGORY}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={ADD_CONTROL_CATEGORY} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_REGULATIONS_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_REGULATIONS_DETAILS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_ACTIVITIES_DUTIES}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_ACTIVITIES_DUTIES} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_ACTIVITIES_ACTIONS}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_ACTIVITIES_ACTIONS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_ACTIVITIES_IMPROVEMENTS}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={PrivacyOperations}
              pathName={PRIVACY_OPS_ACTIVITIES_IMPROVEMENTS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_RISK_REGISTER}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_RISK_REGISTER} />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_RISK_REGISTER_DETAILS}
        element={
          <PrivateRoutes>
            <RiskRegisterStepper />
          </PrivateRoutes>
        }
      />
      <Route
        path={PRIVACY_OPS_RISK_REGISTER_AUDIT}
        element={
          <PrivateRoutes>
            <LazyElement Component={PrivacyOperations} pathName={PRIVACY_OPS_RISK_REGISTER_AUDIT} />
          </PrivateRoutes>
        }
      />
      <Route
        path={BREACH_MANAGEMENT_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement Component={BreachManagement} pathName={BREACH_MANAGEMENT_DASHBOARD} />
          </PrivateRoutes>
        }
      />
      <Route
        path={BREACH_LIST}
        element={
          <PrivateRoutes>
            <LazyElement Component={BreachManagement} pathName={BREACH_LIST} />
          </PrivateRoutes>
        }
      />
      <Route
        path={BREACH_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={BreachManagement} pathName={BREACH_DETAILS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROPA_VIEW}
        element={
          <PrivateRoutes>
            <LazyElement Component={RopaTaskOverview} pathName={ROPA_VIEW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={ROPA_VIEW_AUDIT_LOG}
        element={
          <PrivateRoutes>
            <LazyElement Component={RopaTaskOverview} pathName={ROPA_VIEW_AUDIT_LOG} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_TASK_OVERVIEW}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={DSR_TASK_OVERVIEW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_MY_TASK}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={DSR_MY_TASK} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_TASK_OVERVIEW_VIEW_ASSIGNEE}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={DSR_TASK_OVERVIEW_VIEW_ASSIGNEE} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_MY_TASK_VIEW}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={DSR_MY_TASK_VIEW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_APPROVED}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_TASK_OVERVIEW_APPROVED}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_CREATE}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_TASK_OVERVIEW_CREATE}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_FORM_BUILDER_REVIEW}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_FORM_BUILDER_REVIEW}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={DataSubjectRights}
              pathName={`${DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_REJECTED}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_TASK_OVERVIEW_REJECTED}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_COMPLETED}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={DataSubjectRights}
              pathName={`${DSR_TASK_OVERVIEW_COMPLETED}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_ARCHIVED}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_TASK_OVERVIEW_ARCHIVED}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_VIEW_PENDING_REQ}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={DataSubjectRights}
              pathName={`${DSR_TASK_OVERVIEW_VIEW_PENDING_REQ}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_VIEW_APPROVED_REQ}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={DataSubjectRights}
              pathName={`${DSR_TASK_OVERVIEW_VIEW_APPROVED_REQ}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_VIEW_REJECTED_REQ}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={DataSubjectRights}
              pathName={`${DSR_TASK_OVERVIEW_VIEW_REJECTED_REQ}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_VIEW_REJECTED_IN_PROGRESS}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={DataSubjectRights}
              pathName={`${DSR_TASK_OVERVIEW_VIEW_REJECTED_IN_PROGRESS}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_VIEW_COMPLETED_REQ}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={DataSubjectRights}
              pathName={`${DSR_TASK_OVERVIEW_VIEW_COMPLETED_REQ}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_TASK_OVERVIEW_VIEW_ARCHIVED_REQ}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={DataSubjectRights}
              pathName={`${DSR_TASK_OVERVIEW_VIEW_ARCHIVED_REQ}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_EMAIL_TEMPLATES}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_EMAIL_TEMPLATES}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_REQUEST_FORM}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_REQUEST_FORM}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_FORM_REPOSIOTRY}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_FORM_REPOSIOTRY}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_FORM_BUILDER}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_FORM_BUILDER}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_FORM_BUILDER_CREATE_FORM}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={DataSubjectRights}
              pathName={`${DSR_FORM_BUILDER_CREATE_FORM}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_FORM_BUILDER_VIEW}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_FORM_BUILDER_VIEW}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_ADD_QUESTION}
        element={
          <PrivateRoutes>
            <LazyElement Component={DSRAddQuestion} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DSR_FORM_TRANSLATION}
        element={
          <PrivateRoutes>
            <LazyElement Component={FormTranslation} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_MY_REQUEST}`}
        element={<LazyElement Component={DataSubjectRights} pathName={`${DSR_MY_REQUEST}`} />}
      />
      <Route
        path={`${DSR_MY_REQUEST_DETAILS}`}
        element={
          <LazyElement Component={DataSubjectRights} pathName={`${DSR_MY_REQUEST_DETAILS}`} />
        }
      />
      {/* <Route
        path={`${DSR_ASSIGNEE_VIEW}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_ASSIGNEE_VIEW}`} />
          </PrivateRoutes>
        }
      /> */}
      <Route
        path={`${DSR_ASSIGNEE_VIEW_DETAILS}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_ASSIGNEE_VIEW_DETAILS}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_RETENTION_SCHEDULE}`}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataSubjectRights} pathName={`${DSR_RETENTION_SCHEDULE}`} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COOKIE_CONSENT_MANAGEMENT}
        element={
          <PrivateRoutes>
            <LazyElement Component={CookieConsentManagement} pathName={COOKIE_CONSENT_MANAGEMENT} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COOKIE_CONSENT_NEW_DASHBOAED}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={CookieConsentManagement}
              pathName={COOKIE_CONSENT_NEW_DASHBOAED}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={RECORDED_CONSENTS}
        element={
          <PrivateRoutes>
            <LazyElement Component={CookieConsentManagement} pathName={RECORDED_CONSENTS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COOKIE_DICTIONARY}
        element={
          <PrivateRoutes>
            <LazyElement Component={CookieConsentManagement} pathName={COOKIE_DICTIONARY} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COOKIE_POLICY_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={CookieConsentManagement} pathName={COOKIE_POLICY_DETAILS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VIEW_COOKIE_POLICY}
        element={
          <PrivateRoutes>
            <LazyElement Component={ViewCookiePolicy} pathName={VIEW_COOKIE_POLICY} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COOKIE_CONSENT_DOMAIN}
        element={
          <PrivateRoutes>
            <LazyElement Component={CookieConsentManagement} pathName={COOKIE_CONSENT_DOMAIN} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COOKIE_POLICY}
        element={
          <PrivateRoutes>
            <LazyElement Component={CookieConsentManagement} pathName={COOKIE_POLICY} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COOKIE_POLICY_CREATE}
        element={
          <PrivateRoutes>
            <LazyElement Component={CookieConsentManagement} pathName={COOKIE_POLICY_CREATE} />
          </PrivateRoutes>
        }
      />
      <Route
        path={COOKIE_CONFIGURATION}
        element={
          <PrivateRoutes>
            <LazyElement Component={CookieConsentManagement} pathName={COOKIE_CONFIGURATION} />
          </PrivateRoutes>
        }
      />
      {/* UCM */}
      <Route
        path={UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNIVERSAL_CONSENT_MANAGEMENT_NEW_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UNIVERSAL_CONSENT_MANAGEMENT_NEW_DASHBOARD}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_TEMPLATES}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalConsentManagement} pathName={UCM_TEMPLATES} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_ADD_COLLECTION_TEMPLATE}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_ADD_COLLECTION_TEMPLATE}
            />
          </PrivateRoutes>
        }
      />
      {/* POC Consent Form */}
      <Route
        path={UCM_ADD_COLLECTION_TEMPLATE_POC}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_ADD_COLLECTION_TEMPLATE_POC}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_SUBJECT_CONSENT_TYPES}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_SUBJECT_CONSENT_TYPES}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_SUBJECT_CONSENT_TYPES_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_SUBJECT_CONSENT_TYPES_DETAILS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_SUBJECT_CONSENT_LIST}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_SUBJECT_CONSENT_LIST}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_SUBJECT_CONSENT_MANAGER}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_SUBJECT_CONSENT_MANAGER}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE_DETAILS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_SUBJECT_CONSENT_MANAGER_LIST_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_SUBJECT_CONSENT_MANAGER_LIST_DETAILS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_PRIVACY_NOTICE}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalConsentManagement} pathName={UCM_PRIVACY_NOTICE} />
          </PrivateRoutes>
        }
      />
      <Route
        path={VIEW_UCM_PRIVACY_NOTICE_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={VIEW_UCM_PRIVACY_NOTICE_DETAILS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${UCM_PRIVACY_NOTICE_DETAILS}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_PRIVACY_NOTICE_DETAILS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${UCM_SUBJECT_CONSENT_LIST_DETAILS}`}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={`${UCM_SUBJECT_CONSENT_LIST}`}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_PROCESSING_CATEGORY}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_PROCESSING_CATEGORY}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_PROCESSING_PURPOSE}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalConsentManagement} pathName={UCM_PROCESSING_PURPOSE} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_CONSENT_PURPOSE}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalConsentManagement} pathName={UCM_CONSENT_PURPOSE} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_PII}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalConsentManagement} pathName={UCM_PII} />
          </PrivateRoutes>
        }
      />
      <Route
        path={RETENTION_RULE_LIST}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataRetention} pathName={RETENTION_RULE_LIST} />
          </PrivateRoutes>
        }
      />
      <Route
        path={RETENTION_RULE_DETAILS}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataRetention} pathName={RETENTION_RULE_DETAILS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={RETENTION_RULE_DETAILS_VIEW}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataRetention} pathName={RETENTION_RULE_DETAILS_VIEW} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_RETENTION_DASHBOARD}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataRetention} pathName={DATA_RETENTION_DASHBOARD} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNIVERSAL_CONSENT_MANAGEMENT_FORMCENTRE}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UNIVERSAL_CONSENT_MANAGEMENT_FORMCENTRE}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNIVERSAL_CONSENT_MANAGEMENT_CONSENTUPLOADS}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UNIVERSAL_CONSENT_MANAGEMENT_CONSENTUPLOADS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNIVERSAL_CONSENT_ADD_PREFERENCECENTRE_CENTER}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UNIVERSAL_CONSENT_ADD_PREFERENCECENTRE_CENTER}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNIVERSAL_CONSENT_UPDATE_PREFERENCECENTRE_CENTER}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UNIVERSAL_CONSENT_UPDATE_PREFERENCECENTRE_CENTER}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNIVERSAL_CONSENT_CONSENT_PURPOSE}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UNIVERSAL_CONSENT_CONSENT_PURPOSE}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNIVERSAL_CONSENT_CUSTOME_PARAMETERS}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UNIVERSAL_CONSENT_CUSTOME_PARAMETERS}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={PII_LIST}
        element={
          <PrivateRoutes>
            <LazyElement Component={UnstructuredDataInventory} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UNSTRUCTURED_DATA_MAPPING}
        element={
          <PrivateRoutes>
            <LazyElement Component={UnstructuredDataMapping} />
          </PrivateRoutes>
        }
      ></Route>
      <Route
        path={UCM_COLLECTION_TEMPLATE}
        element={
          <PrivateRoutes>
            <LazyElement Component={CollectionTemplates} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_FORM}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalConsentManagement} pathName={UCM_FORM} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_SOURCE_CONSENT_UPLOAD}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={UniversalConsentManagement}
              pathName={UCM_SOURCE_CONSENT_UPLOAD}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCM_FORM_CREATE}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalConsentManagement} pathName={UCM_FORM_CREATE} />
          </PrivateRoutes>
        }
      />
      {/* Data Governance */}
      <Route
        path={DATA_GOVERNANCE_ASSET_DISCOVERY}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={AssetDiscoveryDashboard}
              pathName={DATA_GOVERNANCE_ASSET_DISCOVERY}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_GOVERNANCE_ASSET_INVENTORY}
        element={
          <PrivateRoutes>
            <LazyElement Component={AssetInventory} pathName={DATA_GOVERNANCE_ASSET_INVENTORY} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_GOVERNANCE_DATA_CLASSIFICATION}
        element={
          <PrivateRoutes>
            <LazyElement
              Component={ClassificationResultsPage}
              pathName={DATA_GOVERNANCE_DATA_CLASSIFICATION}
            />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_GOVERNANCE_CUSTOM_TAGGING}
        element={
          <PrivateRoutes>
            <LazyElement Component={CustomTagging} pathName={DATA_GOVERNANCE_CUSTOM_TAGGING} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_GOVERNANCE_DATA_CATALOGUE}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataGovernanceDataCatalog} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_GOVERNANCE_POLICY_CENTER}
        element={
          <PrivateRoutes>
            <LazyElement Component={PolicyManagement} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_GOVERNANCE_COMPLIANCE_CENTER}
        element={
          <PrivateRoutes>
            <LazyElement Component={ComplianceCenter} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_GOVERNANCE_RISK_ASSESSMENT}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataGovernanceRiskAssessment} />
          </PrivateRoutes>
        }
      />
      <Route
        path={DATA_GOVERNANCE_RISK_ANALYTICS}
        element={
          <PrivateRoutes>
            <LazyElement Component={DataGovernanceAnalytics} />
          </PrivateRoutes>
        }
      />
      {/* UCF */}
      <Route
        path={UCF}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalControlSystem} pathName={UCF} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCF_IMPROVENENT}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalControlSystem} pathName={UCF_IMPROVENENT} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCF_ACTIONS}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalControlSystem} pathName={UCF_ACTIONS} />
          </PrivateRoutes>
        }
      />
      <Route
        path={UCF_ONBOARDING}
        element={
          <PrivateRoutes>
            <LazyElement Component={UniversalControlSystem} pathName={UCF_ONBOARDING} />
          </PrivateRoutes>
        }
      />
      <Route
        path={GOTRUST_PREFERENCE_FORM}
        element={<LazyElement Component={PreferenceForm} pathName={GOTRUST_PREFERENCE_FORM} />}
      />
      <Route
        path={GOTRUST_CONSENT_FORM}
        element={<LazyElement Component={UCMForm} pathName={GOTRUST_CONSENT_FORM} />}
      />
      <Route path={NOTICE} element={<LazyElement Component={Notice} pathName={NOTICE} />} />
      <Route
        path={GOTRUST_COOKIE_POLICY_NOTICE}
        element={<LazyElement Component={CookiePolicyNotice} />}
      />
      <Route
        path={GOTRUST_CONSENT_FORM_VERIFICATION}
        element={<LazyElement Component={ConsentFormVerificaton} />}
      />
      <Route path={UNAUTHORIZED} element={<LazyElement Component={Unauthorized} />} />
      <Route
        path={LOGIN}
        element={
          <PublicRoutes>
            <LazyElement Component={AuthorizationWrapper} pathName={LOGIN} />
          </PublicRoutes>
        }
      />
      <Route
        path={LOGIN_WITH_PARAM}
        element={
          <PublicRoutes>
            <LazyElement Component={AuthorizationWrapper} pathName={LOGIN_WITH_PARAM} />
          </PublicRoutes>
        }
      />
      <Route
        path={ONBOARDING_QUESTIONS}
        element={
          <OnboardingWrapper>
            <Questions />
          </OnboardingWrapper>
        }
      />
      <Route
        path={PRIVACY_CENTER}
        element={
          <PublicRoutes>
            <PrivacyCentreIndex pathName={PRIVACY_CENTER} />
          </PublicRoutes>
        }
      />
      <Route
        path={GOTRUST_PREFERENCE_FORM}
        element={
          <PrivateRoutes>
            <LazyElement Component={PreferenceForm} pathName={GOTRUST_PREFERENCE_FORM} />
          </PrivateRoutes>
        }
      />
      <Route
        path={`${DSR_EMAIL_VER}`}
        element={<LazyElement Component={DataSubjectRights} pathName={`${DSR_EMAIL_VER}`} />}
      />
      <Route
        path={`${DSR_MY_REQUEST}`}
        element={<LazyElement Component={DataSubjectRights} pathName={`${DSR_MY_REQUEST}`} />}
      />
      <Route
        path={`${DSR_MY_REQUEST_DETAILS}`}
        element={
          <LazyElement Component={DataSubjectRights} pathName={`${DSR_MY_REQUEST_DETAILS}`} />
        }
      />
      <Route
        path="/dsr/request-form/:encryptedFormId/:encryptedCustomerId"
        element={<RequestForm />}
      />
      <Route path="/dsr/verify-otp" element={<DsrOtpVerification />} />
      <Route path="/dsr/thank-you" element={<ThankYouPage pathName={''} />} />
      <Route
        path="/data-subject-rights/my-request/email-verification"
        element={<EmaiilVer pathName={''} />}
      />
      <Route path="/*" element={<NotFound />} />
      <Route path="/server-error" element={<InternalServer />} />
    </RouterRoutes>
  );
};

const Routes: React.FC = () => {
  const auth = useAuth();
  const access_token = Cookies.get('access_token');

  const location = useLocation();
  switch (auth.activeNavigator) {
    case 'signinSilent': {
      Cookies.remove('access_token');
      return <div>Signing you in...</div>;
    }
    case 'signoutRedirect': {
      Cookies.remove('access_token');
      return <div>Signing you out...</div>;
    }
  }

  if (auth.isLoading) {
    return <OverlayLoader />;
  }

  if (auth.error) {
    return <ErrorPage errorMessage={auth.error.message} />;
  }

  return <LazyElement Component={AppRoutes} />;
};

export default Routes;
