import httpClient from '../../../api/httpClient';
import {
  FETCH_ROPA_DASHBOARD_COLLABORATOR,
  FETCH_ROPA_DASHBOARD_COMPLETION,
  FETCH_ROPA_DASHBOARD_DATA_SYSTEM,
  FETCH_ROPA_DASHBOARD_DEPT,
  FETCH_ROPA_DASHBOARD_DPIA,
  FETCH_ROPA_DASHBOARD_LAW,
  FETCH_ROPA_DASHBOARD_ORG_ROLE,
  FETCH_ROPA_DASHBOARD_PERSONAL_DATA,
  FETCH_ROPA_DASHBOARD_THIRD_PARTY,
  ROPA_COUNT,
  ROPA_LEVEL,
  ROPA_TASK_OVERVIEW_LIST,
} from '../api';

export const Fetch_Ropa_Dashboard_Dept = async (page?: number, size?: number) => {
  try {
    let url = FETCH_ROPA_DASHBOARD_DEPT;

    // Add pagination parameters if provided
    if (page && size) {
      url += `?page=${page}&size=${size}`;
    }

    const response = await httpClient.get(url);

    if (response.status === 200) {
      return response;
    }
  } catch (error) {
    console.error('There was an error!', error);
  }
};

export const Fetch_Ropa_Dashboard_Completion = async (
  entity_id?: number,
  page?: number,
  size?: number
) => {
  try {
    let url = FETCH_ROPA_DASHBOARD_COMPLETION;
    const params = new URLSearchParams();
    if (entity_id && entity_id !== -1) {
      params.append('entity_id', entity_id.toString());
    }
    if (page && size) {
      params.append('page', page.toString());
      params.append('size', size.toString());
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await httpClient.get(url);

    if (response.status === 200) {
      return response;
    }
  } catch (error) {
    console.error('There was an error!', error);
  }
};

export const Fetch_Ropa_Dashboard_Collaborator = async (entity_id: number) => {
  try {
    const response = await httpClient.get(FETCH_ROPA_DASHBOARD_COLLABORATOR, {
      params: { entity_id }, // This will add ?entity_id=value to the URL
    });

    if (response.status === 200) {
      return response;
    }
  } catch (error) {
    console.error('There was an error!', error);
  }
};

export const Fetch_Ropa_Dashboard_Law = async () => {
  try {
    const response = await httpClient.get(FETCH_ROPA_DASHBOARD_LAW);

    if (response.status === 200) {
      return response;
    }
  } catch (error) {
    console.error('There was an error!', error);
  }
};

export const Fetch_Ropa_Dashboard_Third_Party = async () => {
  try {
    const response = await httpClient.get(FETCH_ROPA_DASHBOARD_THIRD_PARTY);

    if (response.status === 200) {
      return response;
    }
  } catch (error) {
    console.error('There was an error!', error);
  }
};

export const Fetch_Ropa_Dashboard_Personal_data = async () => {
  try {
    const response = await httpClient.get(FETCH_ROPA_DASHBOARD_PERSONAL_DATA);

    if (response.status === 200) {
      return response;
    }
  } catch (error) {
    console.error('There was an error!', error);
  }
};

export const Fetch_Ropa_Dashboard_Data_System = async () => {
  try {
    const response = await httpClient.get(FETCH_ROPA_DASHBOARD_DATA_SYSTEM);

    if (response.status === 200) {
      return response;
    }
  } catch (error) {
    console.error('There was an error!', error);
  }
};

export const Fetch_Ropa_Dashboard_Org_Role = async () => {
  try {
    const response = await httpClient.get(FETCH_ROPA_DASHBOARD_ORG_ROLE);

    if (response.status === 200) {
      return response;
    }
  } catch (error) {
    console.error('There was an error!', error);
  }
};

export const Fetch_Ropa_Dashboard_Dpia = async () => {
  try {
    const response = await httpClient.get(FETCH_ROPA_DASHBOARD_DPIA);

    if (response.status === 200) {
      return response;
    }
  } catch (error) {
    console.error('There was an error!', error);
  }
};

export const fetchRopaStats = async ({ queryKey }: { queryKey: [string, number] }) => {
  const [_key, entity_id] = queryKey;
  try {
    const response = await httpClient.get(`${ROPA_COUNT}?entity_id=${entity_id}`);
    return response;
  } catch (error) {
    throw new Error('Failed to fetch data.');
  }
};

export const fetchRopaLevel = async () => {
  try {
    const response = await httpClient.get(ROPA_LEVEL);
    return response;
  } catch (error) {
    throw new Error('Failed to fetch data.');
  }
};

export const fetchRecentRopa = async ({ queryKey }: { queryKey: [string, number] }) => {
  const [_key, entity_id] = queryKey;
  try {
    const response = await httpClient.get(
      `${ROPA_TASK_OVERVIEW_LIST}/${entity_id}?page=1&size=5&sort_by=createdAt&sort_order=DESC`
    );
    return response;
  } catch (error) {
    throw new Error('Failed to fetch data.');
  }
};
