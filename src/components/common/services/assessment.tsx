import toast from 'react-hot-toast';
import httpClient from '../../../api/httpClient';
import {
  ADD_ASSESSMENT_UNIFIED,
  COLLABORATOR_PROGRESS_ASSESSMENT,
  COMMON_ASSESSMENT_ENDPOINT,
  CREATE_NEW_UNIFIED_ASSESSMENT,
  FETCH_ASSESSMENT_DASHBOARD_COLLABORATOR,
  FETCH_ASSESSMENT_TEMPLATES,
  FETCH_ASSESSMENTS_LIST,
  FETCH_CATEGORY_ASSESSMENTS_LIST,
  FETCH_DASHBOARD_ASSESSMENT_TEMPLATES,
  FETCH_DEPARTMENT_BY_GROUPID,
  FETCH_PROCESS_BY_DEPTID,
  FETCH_REVIEWER_DATA,
  UPLOAD_ASSESSMENT_TEMPLATE,
} from '../api';

export const Fetch_Dashboard_Data = (url: string) => {
  return httpClient.get(`${url}`);
};

export const fetchAssessmentTemplateView = (url: string) => {
  return httpClient.get(`${url}`);
};

export const Fetch_Assessment_Category_List = (url: string) => {
  return httpClient.get(`${url}`);
};

export const fetchDepartments = async (id: number) => {
  try {
    const response = await httpClient.get(`${FETCH_DEPARTMENT_BY_GROUPID}/${id}`);
    return response;
  } catch (error) {
    console.error('Failed to fetch data', error);
    throw new Error('Failed to fetch data.');
  }
};

export const fetchProcesses = async (id: number) => {
  try {
    const response = await httpClient.get(`${FETCH_PROCESS_BY_DEPTID}/${id}`, {});
    return response;
  } catch (error) {
    console.error('Failed to fetch data', error);
    throw new Error('Failed to fetch data.');
  }
};

export const fetchAssessments = async () => {
  try {
    const response = await httpClient.get(`${FETCH_ASSESSMENTS_LIST}`);
    return response.data?.result?.rows;
  } catch (error) {
    console.error('Failed to fetch data', error);
    throw new Error('Failed to fetch data.');
  }
};

export const createNewAssessment = async (body: any) => {
  try {
    const response = await httpClient.post(`${CREATE_NEW_UNIFIED_ASSESSMENT}`, body);
    if (response) {
      toast.dismiss();
      toast.success('Assessment created successfully');
    }
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch data', error);
    toast.dismiss();
    toast.error(error?.response?.data?.message || 'Failed to create assessment');
    throw new Error(error?.message || 'Failed to create assessment.');
  }
};

export const Fetch_Assessment_Control_Assign_Data = (
  searchValue: string,
  customer_id: number,
  group_id: number | null
) => {
  // let query = `?customer_id=${customer_id}&group_id=${group_id}`;
  const user_type = 'active';
  let query = `?customer_id=${customer_id}&id=${group_id}&type=Group&user_type=${user_type}`;
  if (searchValue) {
    query += `&search=${searchValue}`;
  }
  return httpClient.get(`${FETCH_REVIEWER_DATA}${query}`);
};

/// ******************** Get Add collaborator ROPA ************************ /

export const Fetch_Add_Collaborator_Assessment = (url: string) => {
  return httpClient.get(`${url}`);
};
//***ADD COLLABORATOR****//

export const sendAssessmentCollaborator = (
  assessment_id: number,
  assessment_key: string,
  collaborators: any[]
) => {
  const url = `${COMMON_ASSESSMENT_ENDPOINT}/collaborator`;
  return httpClient.post(`${url}`, {
    assessment_id: assessment_id,
    collaborators,
  });
};

export const uploadAssessmentTemplate = async (body: FormData) => {
  try {
    const response = await httpClient.post(UPLOAD_ASSESSMENT_TEMPLATE, body, {});
    return response.data;
  } catch (error) {
    console.error('Error:', error);
    return error;
  }
};

interface UploadResponse {
  result: any;
  status: number;
  message: string;
}

export const documentServiceAssessment = {
  uploadDocuments: async (files: File[], assessment_id: number): Promise<UploadResponse> => {
    const formData = new FormData();

    files.forEach((file) => {
      formData.append('files', file);
    });

    try {
      // For now, we'll use a generic assessment auto fill endpoint
      // This can be updated when specific endpoints are available for different assessment types
      const response = await httpClient.post<UploadResponse>(
        `${COMMON_ASSESSMENT_ENDPOINT}/automate-assessment/${assessment_id}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Upload failed:', error);
      throw new Error(error.response?.data?.message || 'Upload failed. Please try again.');
    }
  },
};

export const fetchAssessmentTemplates = async (search: string) => {
  let url = FETCH_ASSESSMENT_TEMPLATES;
  if (search !== '') {
    url += `?search=${search}`;
  }
  try {
    const response = await httpClient.get(`${url}`);
    return response;
  } catch (error) {
    console.error('Failed to fetch template data', error);
    throw new Error('Failed to fetch template data.');
  }
};

export const fetchAssessmentTemplatesDashboard = async (search: string) => {
  let url = FETCH_DASHBOARD_ASSESSMENT_TEMPLATES;
  if (search !== '') {
    url += `?search=${search}`;
  }
  try {
    const response = await httpClient.get(`${url}`);
    return response;
  } catch (error) {
    console.error('Failed to fetch template data', error);
    throw new Error('Failed to fetch template data.');
  }
};

export const fetchAssessmentCollaboratorDashboard = async (search: string) => {
  let url = FETCH_ASSESSMENT_DASHBOARD_COLLABORATOR;
  if (search !== '') {
    url += `?search=${search}`;
  }
  try {
    const response = await httpClient.get(`${url}`);
    return response;
  } catch (error) {
    console.error('Failed to fetch template data', error);
    throw new Error('Failed to fetch template data.');
  }
};

export const fetchAssessmentList = async () => {
  try {
    const response = await httpClient.get(`${FETCH_ASSESSMENTS_LIST}`);
    return response;
  } catch (error) {
    console.error('Failed to fetch template data', error);
    throw new Error('Failed to fetch template data.');
  }
};

export const createOtherAssessment = async (body: any) => {
  try {
    const response = await httpClient.post(`${ADD_ASSESSMENT_UNIFIED}`, body);
    if (response) {
      toast.dismiss();
    }
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch data', error);
    toast.dismiss();
    toast.error(error?.response?.data?.message || 'Failed to create assessment');
    throw new Error(error?.message || 'Failed to create assessment.');
  }
};

export const fetchCategoryAssessmentList = async () => {
  try {
    const response = await httpClient.get(`${FETCH_CATEGORY_ASSESSMENTS_LIST}`);
    return response;
  } catch (error) {
    console.error('Failed to fetch template data', error);
    throw new Error('Failed to fetch template data.');
  }
};

export const fetchAssessmentCollaboratorProgress = async (assessment_id: number) => {
  try {
    const response = await httpClient.get(`${COLLABORATOR_PROGRESS_ASSESSMENT}/${assessment_id}`);
    return response;
  } catch (error) {
    console.error('Failed to fetch collaborator data', error);
    throw new Error('Failed to fetch collaborator data.');
  }
};
