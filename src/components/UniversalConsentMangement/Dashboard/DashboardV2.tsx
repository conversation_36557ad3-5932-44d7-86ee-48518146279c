import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { Alert<PERSON>riangle, Refresh<PERSON>w, UserCheck, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Card, CardContent, CardHeader, CardTitle } from '../../../@/components/ui/card';
import SearchableSelect from '../../../@/components/ui/Common/Elements/Select/SearchableSelect';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../@/components/ui/tabs';
import httpClient from '../../../api/httpClient';
import { useUcmDashboard } from '../../../hooks/universal-consent-management/useUcmDashboard';
import { RootState } from '../../../redux/store';
import {
  ConsentRecordsTableProperties,
  EntityProperties,
} from '../../../types/universal-consent-management';
import { FETCH_ENTITIES } from '../../common/api';
import { ChartCardLoader } from '../../common/LoadingUI';
import {
  fetchDashboardConsentRecordsTable,
  get_ucm_collection_builder_dashboard_data,
} from '../../common/services/universal-consent-management';
import AnalyticsTab from './AnalyticsTab';
import CollectionTemplatesTab from './CollectionTemplatesTab';
import ComplianceDashboardTab from './ComplianceDashboardTab';
// import { FlippableCard } from './FlippableCard';

const Dashboard: React.FC = () => {
  //! Variables
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const [selectedEntityId, setSelectedEntityId] = useState<string>('0');

  // CUSTOM HOOK CALL

  const {
    data: collectionBuilderDashboardData,
    isLoading: isLoadingCollectionBuilderDashboardData,
  } = useQuery({
    queryKey: ['collectionBuilderDashboardData', customer_id, selectedEntityId],
    queryFn: () => fetchCollectionBuilderDashboardData(),
    enabled: !!customer_id,
  });

  console.log(collectionBuilderDashboardData, 'collectionBuilderDashboardData123');

  const fetchCollectionBuilderDashboardData = async () => {
    const response = await get_ucm_collection_builder_dashboard_data(
      customer_id,
      Number(selectedEntityId)
    );
    console.log(response, 'response123');
    return response.result.data;
  };

  const { cardsData, isCardDataLoading } = useUcmDashboard();
  console.log(cardsData, ' card Data');

  //! STATES
  const [consentRecordsTable, setConsentRecordsTable] = useState<ConsentRecordsTableProperties[]>(
    []
  );
  // const [entities, setEntities] = useState<EntityProperties[]>([]);
  const [timeFrame, setTimeFrame] = useState<string>('90');
  const [isLoadingStats, setIsLoadingStats] = useState<boolean>(true);

  const { t } = useTranslation();

  // fetch consent records table
  useEffect(() => {
    const fetchConsentRecordsTable = async () => {
      try {
        // Send the data to the API and await the response
        const response = await fetchDashboardConsentRecordsTable(
          customer_id,
          timeFrame,
          selectedEntityId
        );

        if (response?.status_code === 200) {
          setConsentRecordsTable(response?.result?.data);
        }
      } catch (error) {
        // eslint-disable-next-line unicorn/prefer-ternary
        if (axios.isAxiosError(error)) {
          setConsentRecordsTable([]);
          // Axios specific error handling
          toast.dismiss(); // Clear any existing toasts
          // const status = error?.response?.data?.status_code;
          // const statusText = error?.response?.data?.message;
          const errorMessage = error?.response?.data?.result?.error || error.message;
          toast.error(`${errorMessage}`);
          console.error('Axios Error:', error);
        } else {
          // Generic error handling
          toast.dismiss();
          toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.unexpectedErrorOccurred'));
          console.error('Unexpected Error:', error);
        }
      }
    };

    if (customer_id) fetchConsentRecordsTable();
  }, [customer_id, selectedEntityId]);

  //fetching entities

  const { data: entities } = useQuery<EntityProperties[]>({
    queryKey: ['entities', customer_id],
    queryFn: async () => {
      const responseData = await httpClient.get(`${FETCH_ENTITIES}${customer_id}`);
      const result = responseData?.data?.result?.rows;
      result.unshift({
        id: 0,
        name: 'All Domains',
        user_id: 0,
        spoc_id: 0,
        status: 'active',
        createdAt: '',
        updatedAt: '',
        deletedAt: null,
        parent_id: 0,
        customer_id: 0,
      });
      return result;
    },
    enabled: !!customer_id,
  });

  //IMPORTS FOR NEW DASHBOARD
  const [activeTab, setActiveTab] = useState('templates');

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Universal Consent Management</h1>
          </div>
          <div className="flex justify-between">
            <div className=" font-medium flex justify-center items-center gap-3 rounded-md bg-yellow-50 p-1 shadow-sm">
              <AlertTriangle className="text-yellow-600 size-6" />
              <div>
                <p className="text-sm text-yellow-700">
                  The data displayed reflects information collected up to 24 hours prior
                </p>
              </div>
            </div>

            <SearchableSelect
              placeholder="All Domains"
              options={
                entities?.map((entity: any) => ({ value: entity.name, id: entity.id })) || []
              }
              value={selectedEntityId.toString()}
              onChange={(value) => setSelectedEntityId(value)}
              className="h-10 w-48 ml-6"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
          {/* <FlippableCard
            title="Active Consents"
            icon={<UserCheck className="h-4 w-4 text-slate-400" />}
            data={cardsData?.active_consents}
          /> */}
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Total Consents</CardTitle>
                <AlertTriangle className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.total_consents?.total_value ??
                    '-'}
                </div>
                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.total_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {' '}
                  <span>
                    {collectionBuilderDashboardData?.common_metrics?.total_consents
                      ?.percentage_change_from_last_month ?? '-'}
                  </span>
                  <span
                    className={`${
                      collectionBuilderDashboardData?.common_metrics?.total_consents
                        ?.percentage_change_from_last_month >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}
                  >
                    % from last month
                  </span>
                </p>
              </CardContent>
            </Card>
          )}

          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Active Consents</CardTitle>
                <UserCheck className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.active_consents?.total_value ??
                    '-'}
                </div>

                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.active_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {' '}
                  <span>
                    {collectionBuilderDashboardData?.common_metrics?.active_consents
                      ?.percentage_change_from_last_month ?? '-'}
                  </span>
                  <span
                    className={`${
                      collectionBuilderDashboardData?.common_metrics?.active_consents
                        ?.percentage_change_from_last_month >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}
                  >
                    % from last month
                  </span>
                </p>
              </CardContent>
            </Card>
          )}
          {/* <FlippableCard
            title="Total Consents"
            icon={<Users className="h-4 w-4 text-slate-400" />}
            data={cardsData?.total_consents}
          /> */}
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Declined Consents</CardTitle>
                <Users className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.declined_consents?.total_value ??
                    '-'}
                </div>
                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.declined_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {' '}
                  <span>
                    {collectionBuilderDashboardData?.common_metrics?.declined_consents
                      ?.percentage_change_from_last_month ?? '-'}
                  </span>
                  <span
                    className={`${
                      collectionBuilderDashboardData?.common_metrics?.declined_consents
                        ?.percentage_change_from_last_month >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}
                  >
                    % from last month
                  </span>
                </p>
              </CardContent>
            </Card>
          )}
          {/* <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
              <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
              <Shield className="h-4 w-4 text-slate-400" />
            </CardHeader>
            <CardContent className="px-5">
              <div className="text-2xl font-bold text-green-600">96.8%</div>
              <p className="text-xs text-slate-600">Across all frameworks</p>
            </CardContent>
          </Card> */}
          {/* <FlippableCard
            title="Withdrawal Consents"
            icon={<AlertTriangle className="h-4 w-4 text-slate-400" />}
            data={cardsData?.withdrawal_consents}
          /> */}
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Withdrawal Consents</CardTitle>
                <AlertTriangle className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.withdrawal_consents
                    ?.total_value ?? '-'}
                </div>
                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.withdrawal_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {' '}
                  <span>
                    {collectionBuilderDashboardData?.common_metrics?.withdrawal_consents
                      ?.percentage_change_from_last_month ?? '-'}
                  </span>
                  <span
                    className={`${
                      collectionBuilderDashboardData?.common_metrics?.withdrawal_consents
                        ?.percentage_change_from_last_month >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}
                  >
                    % from last month
                  </span>
                </p>
              </CardContent>
            </Card>
          )}
          {/* <FlippableCard
            title="Renewal Consents"
            icon={<RefreshCw className="h-4 w-4 text-slate-400" />}
            data={cardsData?.renewal_consents}
          /> */}
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Renewal Consents</CardTitle>
                <RefreshCw className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5 pb-6">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.renewal_consents?.total_value ??
                    '-'}
                </div>
                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.renewal_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  <span>
                    {collectionBuilderDashboardData?.common_metrics?.renewal_consents
                      ?.percentage_change_from_last_month ?? '-'}
                  </span>
                  <span
                    className={`${
                      collectionBuilderDashboardData?.common_metrics?.renewal_consents
                        ?.percentage_change_from_last_month >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}
                  >
                    % from last month
                  </span>
                </p>
              </CardContent>
            </Card>
          )}
          {/* <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
              <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
              <Zap className="h-4 w-4 text-slate-400" />
            </CardHeader>
            <CardContent className="px-5 pb-6">
              <div className="text-2xl font-bold">180ms</div>
              <p className="text-xs text-slate-600">Avg response time</p>
            </CardContent>
          </Card> */}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="templates">Collection Templates</TabsTrigger>
            {/* <TabsTrigger value="ucm-lab">UCM Lab Foundation</TabsTrigger> */}
            {/* <TabsTrigger value="lifecycle">Consent Lifecycle</TabsTrigger> */}
            {/* <TabsTrigger value="audit">Audit Trail</TabsTrigger> */}
            {/* <TabsTrigger value="workflows">Workflows</TabsTrigger> */}
            {/* <TabsTrigger value="settings">Settings</TabsTrigger> */}
          </TabsList>

          {/* UCM Lab Foundation Setup */}
          {/* <TabsContent value="ucm-lab" className="space-y-6">
            <UcmLabFoundationTab
              setUcmLabSetupComplete={setUcmLabSetupComplete}
            />
          </TabsContent> */}

          {/* Collection Templates Management */}
          <TabsContent value="templates" className="space-y-6">
            <CollectionTemplatesTab
              collectionBuilderDashboardData={collectionBuilderDashboardData}
              selectedEntityId={selectedEntityId}
              isLoadingCollectionBuilderDashboardData={isLoadingCollectionBuilderDashboardData}
            />
          </TabsContent>

          {/* Consent Lifecycle Management */}
          {/* <TabsContent value="lifecycle" className="space-y-6">
            <ConsentLifecycleTab />
          </TabsContent> */}

          {/* Compliance Dashboard */}
          <TabsContent value="compliance" className="space-y-8">
            <ComplianceDashboardTab />
          </TabsContent>

          {/* Audit Trail */}
          {/* <TabsContent value="audit" className="space-y-8">
            <AuditTrailTab />
          </TabsContent> */}

          {/* Workflow Management */}
          {/* <TabsContent value="workflows" className="space-y-6">
            <WorkflowsTab />
          </TabsContent> */}

          {/* Analytics */}
          <TabsContent value="analytics" className="space-y-8">
            <AnalyticsTab />
          </TabsContent>

          {/* Settings */}
          {/* <TabsContent value="settings" className="space-y-6">
            <SettingsTab />
          </TabsContent> */}
        </Tabs>
      </div>
    </>
  );
};

export default Dashboard;
