import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../../../../../@/components/ui/Common/Elements/Accordian/Accordian';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardHeader,
} from '../../../../../@/components/ui/Common/Elements/Card/Card';
import {
  PrivacyNoticeRecordData,
  SubjectListRecordData,
} from '../../../../../types/universal-consent-management';
import { convertDateToHumanView, convertString } from '../../../../common/CommonHelperFunctions';
import DynamicTable from '../../../../common/ShadcnDynamicTable/dynamic-table';

const TemplateAccordion = ({
  subjectListRecord,
}: {
  subjectListRecord: SubjectListRecordData | undefined;
}) => {
  const { t } = useTranslation();
  const TemplatesTableColumns: ColumnDef<PrivacyNoticeRecordData>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.SubjectConsentManager.ConsentName')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const name: string = row.getValue('name');
        return <div>{name ?? '-'}</div>;
      },
    },
    {
      accessorKey: 'pii_label_name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.SubjectConsentManager.PIILabelName')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const pii_name: string = row.getValue('pii_label_name');
        return <div>{pii_name ?? '-'}</div>;
      },
    },
    {
      accessorKey: 'consent_source',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.SubjectConsentManager.Source')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const source: string = row.getValue('consent_source');
        return <div className="">{source ? convertString(source) : '-'}</div>;
      },
    },
    {
      accessorKey: 'consent_status',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.SubjectConsentManager.Status')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const status: string = row.getValue('consent_status');
        return <div className="">{status ? convertString(status) : '-'}</div>;
      },
    },
    {
      accessorKey: 'frequency',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.SubjectConsentManager.Frequency')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const frequency: string = row.getValue('frequency');
        return <div className="">{frequency ? convertString(frequency) : '-'}</div>;
      },
    },
    {
      accessorKey: 'geolocation',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.SubjectConsentManager.GeoLocation')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const geolocation: string = row.getValue('geolocation');
        return <div>{geolocation ?? '-'}</div>;
      },
    },
    {
      accessorKey: 'consented_at',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.SubjectConsentManager.ConsentedAt')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const consented_at: string | number | Date = row.getValue('consented_at');
        return <div>{consented_at ? convertDateToHumanView(consented_at.toString()) : '-'}</div>;
      },
    },
    {
      accessorKey: 'consent_expiration',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.SubjectConsentManager.ConsentExpiration')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const expiration: string | number | Date = row.getValue('consent_expiration');
        return (
          <div className="">{expiration ? convertDateToHumanView(expiration.toString()) : '-'}</div>
        );
      },
    },
  ];

  return (
    <div className="mt-4 w-full border-2 border-[#E2E8F0]">
      <Card className="bg-[#FCFCFC]">
        <CardHeader className="font-semibold">List of Template Table</CardHeader>
        <CardContent>
          {subjectListRecord?.consent_record_data?.length ? (
            <Accordion
              type="multiple"
              className="w-full"
              defaultValue={
                subjectListRecord?.consent_record_data
                  ? [subjectListRecord?.consent_record_data[0]?.collection_template_id.toString()]
                  : ['']
              }
            >
              {subjectListRecord?.consent_record_data?.map((record) => (
                <AccordionItem
                  value={record?.collection_template_id.toString()}
                  key={record?.collection_template_id.toString()}
                >
                  <AccordionTrigger className="font-normal hover:no-underline">
                    {record?.collection_template_name ?? '-'}
                  </AccordionTrigger>
                  <AccordionContent>
                    {record?.ucr_data?.length ? (
                      <Accordion type="multiple" defaultValue={['']} className="w-full">
                        {record?.ucr_data?.map((identity) => (
                          <AccordionItem
                            value={`${identity?.data_principal_id.toString()}`}
                            key={`${identity?.data_principal_id?.toString()}`}
                          >
                            <AccordionTrigger className="bg-[#E2E8F0] pl-5 font-normal hover:no-underline">
                              {identity?.subject_identity}
                            </AccordionTrigger>
                            <AccordionContent className="overflow-auto pl-5">
                              {identity?.record_data?.length ? (
                                <DynamicTable<PrivacyNoticeRecordData>
                                  data={identity?.record_data}
                                  columns={TemplatesTableColumns}
                                  enableSorting
                                />
                              ) : (
                                <p className="text-center">No result.</p>
                              )}
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    ) : (
                      <p className="text-center">No result.</p>
                    )}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          ) : (
            <p className="text-center">No result.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TemplateAccordion;
