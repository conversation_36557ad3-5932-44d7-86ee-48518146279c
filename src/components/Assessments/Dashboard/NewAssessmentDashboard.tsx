import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '../../../@/components/ui/tabs';
import {
  AssessmentsTab,
  AuditTab,
  CollaborationTab,
  HeaderSection,
  MetricsDashboard,
  TemplatesTab,
} from './components';

export default function Assessment() {
  const [searchTerm, setSearchTerm] = useState('');

  const handleNewAssessment = () => {
    console.log('Creating new assessment...');
  };

  const handleImportCSV = () => {
    console.log('Importing CSV...');
  };

  return (
    <div className="space-y-6 p-4">
      <HeaderSection onNewAssessment={handleNewAssessment} onImportCSV={handleImportCSV} />

      <MetricsDashboard />

      <Tabs defaultValue="assessments" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="assessments">Assessments</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
          <TabsTrigger value="audit">Audit</TabsTrigger>
          {/* <TabsTrigger value="risk-management">Risk</TabsTrigger> */}
          {/* <TabsTrigger value="workflows">Workflows</TabsTrigger> */}
          {/* <TabsTrigger value="notifications">Notifications</TabsTrigger> */}
          {/* <TabsTrigger value="reports">Reports</TabsTrigger> */}
          {/* <TabsTrigger value="analytics">Analytics</TabsTrigger> */}
          {/* <TabsTrigger value="settings">Settings</TabsTrigger> */}
        </TabsList>

        <TabsContent value="assessments" className="space-y-6">
          <AssessmentsTab />
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <TemplatesTab />
        </TabsContent>

        <TabsContent value="collaboration" className="space-y-6">
          <CollaborationTab />
        </TabsContent>

        {/* <TabsContent value="risk-management" className="space-y-6">
          <RiskManagementTab />
        </TabsContent> */}

        {/* <TabsContent value="workflows" className="space-y-6">
          <WorkflowsTab />
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <NotificationsTab />
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <ReportsTab />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AnalyticsTab />
        </TabsContent> */}

        <TabsContent value="audit" className="space-y-6">
          <AuditTab searchValue={searchTerm} />
        </TabsContent>

        {/* <TabsContent value="settings" className="space-y-6">
          <SettingsTab />
        </TabsContent> */}
      </Tabs>
    </div>
  );
}
