import { TrendingUp } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import { Badge } from '../../../../@/components/ui/badge';

interface RiskMetric {
  category: string;
  count: number;
  percentage: number;
  color: string;
}

interface MitigationPlan {
  title: string;
  priority: string;
  status: string;
}

interface RiskAssessment {
  assessment: string;
  riskLevel: string;
  impact: string;
  likelihood: string;
  mitigationStatus: string;
  owner: string;
  dueDate: string;
}

interface RiskManagementTabProps {
  riskMetrics?: RiskMetric[];
  mitigationPlans?: MitigationPlan[];
  riskAssessments?: RiskAssessment[];
}

export function RiskManagementTab({
  riskMetrics = [],
  mitigationPlans = [],
  riskAssessments = [],
}: RiskManagementTabProps) {
  const defaultRiskMetrics: RiskMetric[] = [
    { category: 'High Risk', count: 8, percentage: 15, color: 'bg-red-500' },
    { category: 'Medium Risk', count: 23, percentage: 43, color: 'bg-yellow-500' },
    { category: 'Low Risk', count: 22, percentage: 42, color: 'bg-green-500' },
  ];

  const defaultMitigationPlans: MitigationPlan[] = [
    {
      title: 'Data Encryption',
      priority: 'High priority',
      status: 'In Progress',
    },
    {
      title: 'Access Controls',
      priority: 'Medium priority',
      status: 'Planned',
    },
  ];

  const defaultRiskAssessments: RiskAssessment[] = [
    {
      assessment: 'Privacy Impact Assessment',
      riskLevel: 'Medium',
      impact: 'High',
      likelihood: 'Medium',
      mitigationStatus: 'In Progress',
      owner: 'Sarah Johnson',
      dueDate: '2024-02-15',
    },
    {
      assessment: 'Vendor Risk Assessment',
      riskLevel: 'High',
      impact: 'High',
      likelihood: 'High',
      mitigationStatus: 'Planned',
      owner: 'Mike Chen',
      dueDate: '2024-02-10',
    },
  ];

  const displayRiskMetrics = riskMetrics.length > 0 ? riskMetrics : defaultRiskMetrics;
  const displayMitigationPlans =
    mitigationPlans.length > 0 ? mitigationPlans : defaultMitigationPlans;
  const displayRiskAssessments =
    riskAssessments.length > 0 ? riskAssessments : defaultRiskAssessments;

  const getRiskVariant = (riskLevel: string) => {
    switch (riskLevel) {
      case 'High':
        return 'destructive';
      case 'Medium':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getMitigationVariant = (status: string) => {
    switch (status) {
      case 'In Progress':
        return 'outline';
      case 'Planned':
        return 'secondary';
      default:
        return 'default';
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Risk Distribution</CardTitle>
            <CardDescription>Current risk levels across assessments</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {displayRiskMetrics.map((risk, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`h-3 w-3 rounded-full ${risk.color}`}></div>
                    <span className="text-sm font-medium">{risk.category}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-bold">{risk.count}</span>
                    <span className="text-xs text-gray-500">({risk.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Risk Trends</CardTitle>
            <CardDescription>Risk level changes over time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex h-32 items-center justify-center text-gray-500">
              <TrendingUp className="mr-2 h-8 w-8" />
              <span>Trend chart visualization</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Mitigation Plans</CardTitle>
            <CardDescription>Active risk mitigation efforts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {displayMitigationPlans.map((plan, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg border p-3"
                >
                  <div>
                    <h4 className="text-sm font-medium">{plan.title}</h4>
                    <p className="text-xs text-gray-600">{plan.priority}</p>
                  </div>
                  <Badge variant={getMitigationVariant(plan.status)}>{plan.status}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Risk Assessment Matrix</CardTitle>
          <CardDescription>Comprehensive risk evaluation across all assessments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="p-2 text-left">Assessment</th>
                  <th className="p-2 text-left">Risk Level</th>
                  <th className="p-2 text-left">Impact</th>
                  <th className="p-2 text-left">Likelihood</th>
                  <th className="p-2 text-left">Mitigation Status</th>
                  <th className="p-2 text-left">Owner</th>
                  <th className="p-2 text-left">Due Date</th>
                </tr>
              </thead>
              <tbody>
                {displayRiskAssessments.map((assessment, index) => (
                  <tr key={index} className="border-b">
                    <td className="p-2">{assessment.assessment}</td>
                    <td className="p-2">
                      <Badge variant={getRiskVariant(assessment.riskLevel)}>
                        {assessment.riskLevel}
                      </Badge>
                    </td>
                    <td className="p-2">{assessment.impact}</td>
                    <td className="p-2">{assessment.likelihood}</td>
                    <td className="p-2">
                      <Badge variant={getMitigationVariant(assessment.mitigationStatus)}>
                        {assessment.mitigationStatus}
                      </Badge>
                    </td>
                    <td className="p-2">{assessment.owner}</td>
                    <td className="p-2">{assessment.dueDate}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
