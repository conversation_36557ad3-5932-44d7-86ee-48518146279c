import { Eye } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Badge } from '../../../../@/components/ui/badge';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import { Card, CardContent } from '../../../../@/components/ui/Common/Elements/Card/Card';
import { ASSESSMENT__TEMPLATES_VIEW } from '../../../../utils/routeConstant';
import { fetchAssessmentTemplatesDashboard } from '../../../common/services/assessment';

interface Template {
  id: number;
  name: string;
  Key: string;
  url: string | null;
  published: boolean | null;
  controls: number;
  categories: number;
  created_at: string;
  updated_at: string;
  assessment_id: number;
}

export function TemplatesTab({ onCreateTemplate }: { onCreateTemplate?: () => void }) {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetchAssessmentTemplatesDashboard('');
        if (response?.data?.success) {
          setTemplates(response.data.result.templates);
        } else {
          setError('Failed to fetch templates');
        }
      } catch (err) {
        setError('Error loading templates');
        console.error('Error:', err);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  const renderTemplate = (template: Template) => {
    return (
      <div key={template.id} className="rounded-lg border p-4 transition-all">
        <div className="mb-3 flex justify-between pb-2">
          <h3 className="text-lg font-semibold">{template.name}</h3>
          <Badge className="px-3 text-sm" variant="secondary" color="bg-custom-primary">
            {template.Key.toUpperCase()}
          </Badge>
        </div>

        <div className="space-y-3">
          <div className="flex justify-between pb-2 text-sm">
            <span className="text-gray-600">Controls</span>
            <span>{template.controls}</span>
          </div>
          <div className="flex justify-between pb-2 text-sm">
            <span className="text-gray-600">Categories</span>
            <span>{template.categories}</span>
          </div>
          <div className="flex justify-between border-b pb-2 text-sm">
            <span className="text-gray-600">Created</span>
            <span>{new Date(template.created_at).toLocaleDateString()}</span>
          </div>

          <div className="pt-3">
            <Button
              variant="outline"
              size="sm"
              className="w-full border border-gray-300"
              onClick={() => {
                navigate(ASSESSMENT__TEMPLATES_VIEW, {
                  state: {
                    template,
                    assessmentId: template?.assessment_id,
                    templateId: template.id,
                  },
                });
              }}
            >
              <Eye className="mr-2 h-4 w-4" />
              Preview
            </Button>
          </div>
        </div>
      </div>
    );
  };

  if (loading) return <div className="py-8 text-center">Loading...</div>;
  if (error) return <div className="py-8 text-center text-red-500">{error}</div>;

  return (
    <Card className="rounded-lg">
      <CardContent className="p-3">
        <div className="flex items-center justify-between pb-4">
          <h2 className="text-xl font-semibold">Assessment Templates</h2>
          {/* <Button
            onClick={onCreateTemplate}
            className="border-custom-primary-dark border bg-custom-primary hover:bg-custom-primary"
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Template
          </Button> */}
        </div>

        {templates.length > 0 ? (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {templates.map(renderTemplate)}
          </div>
        ) : (
          <div className="rounded-lg border py-8 text-center">No templates available</div>
        )}
      </CardContent>
    </Card>
  );
}
