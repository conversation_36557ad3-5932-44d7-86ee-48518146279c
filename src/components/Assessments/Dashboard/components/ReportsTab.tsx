import { Download, FileText } from 'lucide-react';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import { Input } from '../../../../@/components/ui/Common/Elements/Input/Input';
import { Label } from '../../../../@/components/ui/Common/Elements/Label/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../@/components/ui/Common/Elements/Select/Select';

interface StandardReport {
  name: string;
  description: string;
}

interface ReportsTabProps {
  standardReports?: StandardReport[];
  onGenerateStandardReport?: (reportName: string) => void;
  onGenerateCustomReport?: (reportData: any) => void;
}

export function ReportsTab({
  standardReports = [],
  onGenerateStandardReport,
  onGenerateCustomReport,
}: ReportsTabProps) {
  const defaultStandardReports: StandardReport[] = [
    {
      name: 'Executive Summary Report',
      description: 'High-level assessment overview',
    },
    {
      name: 'Detailed Assessment Report',
      description: 'Comprehensive findings and recommendations',
    },
    {
      name: 'Risk Assessment Matrix',
      description: 'Risk analysis and mitigation plans',
    },
  ];

  const displayStandardReports =
    standardReports.length > 0 ? standardReports : defaultStandardReports;

  const handleCustomReportGeneration = () => {
    // This would collect form data and call the callback
    onGenerateCustomReport?.({
      name: 'Custom Report',
      type: 'summary',
      dateRange: 'last-30',
    });
  };

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Standard Reports</CardTitle>
          <CardDescription>Pre-configured assessment reports</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {displayStandardReports.map((report, index) => (
              <div key={index} className="flex items-center justify-between rounded-lg border p-4">
                <div>
                  <h4 className="font-medium">{report.name}</h4>
                  <p className="text-sm text-gray-600">{report.description}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onGenerateStandardReport?.(report.name)}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Generate
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Custom Reports</CardTitle>
          <CardDescription>Build tailored assessment reports</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="report-name">Report Name</Label>
              <Input id="report-name" placeholder="Enter report name" />
            </div>
            <div>
              <Label htmlFor="report-type">Report Type</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">Summary Report</SelectItem>
                  <SelectItem value="detailed">Detailed Report</SelectItem>
                  <SelectItem value="compliance">Compliance Report</SelectItem>
                  <SelectItem value="risk">Risk Report</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="date-range">Date Range</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-30">Last 30 days</SelectItem>
                  <SelectItem value="last-90">Last 90 days</SelectItem>
                  <SelectItem value="last-year">Last year</SelectItem>
                  <SelectItem value="custom">Custom range</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button className="w-full" onClick={handleCustomReportGeneration}>
              <FileText className="mr-2 h-4 w-4" />
              Generate Custom Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
