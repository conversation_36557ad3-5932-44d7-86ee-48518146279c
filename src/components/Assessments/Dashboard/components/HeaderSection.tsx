import { useSelector } from 'react-redux';
import StartNewAssessmentModal from '../../../Assessments/TaskOverview/StartNewAssessmentModal/StartNewAssessmentModal';

interface HeaderSectionProps {
  onNewAssessment?: () => void;
  onImportCSV?: () => void;
}

export function HeaderSection({ onNewAssessment, onImportCSV }: HeaderSectionProps) {
  const loginData = useSelector((state: any) => state.auth.login.login_details);

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-slate-900">Assessment Management</h1>
        <p className="mt-2 text-slate-600">
          Comprehensive assessment framework for regulatory compliance and risk management
        </p>
      </div>
      <div className="flex space-x-3">
        {/* <Button variant="outline" className="bg-white" onClick={onImportCSV}>
          <Upload className="mr-2 h-4 w-4" />
          Import CSV
        </Button> */}
        {loginData?.role === 'Data Protection Officer' ? <StartNewAssessmentModal /> : <></>}
      </div>
    </div>
  );
}
