import { ColumnDef } from '@tanstack/react-table';
import axios from 'axios';
import { t } from 'i18next';
import {
  AlertTriangle,
  ArrowUpDown,
  CheckCircle,
  Clock,
  PlayCircle,
  ScanEye,
  XCircle,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useAuth } from 'react-oidc-context';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import { Progress } from '../../../../@/components/ui/Common/Elements/ProgressBar/ProgressBar';
import { Badge } from '../../../../@/components/ui/badge';
import solidEye from '../../../../assets/IconoirEyeSolid.svg';
import assessmentDownload from '../../../../assets/policyDownload.svg';
import { downloadAssessment } from '../../../../redux/actions/Assessment/AssessmentActions';
import {
  fetchAssessmentTaskOverviewTable,
  fetchEntities,
} from '../../../../redux/actions/AssessmentTaskOverview/AssessmentTaskOverviewActions';
import { riskAssessmentActions } from '../../../../redux/reducers/Assessment/AssessmentSlice';
import { AppDispatch, RootState } from '../../../../redux/store';
import { encryptId } from '../../../../utils/cipher';
import { api_key_work as api_key, getInitialsByName } from '../../../../utils/helperData';
import {
  ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
  ASSESSMENT_REVIEW,
  ASSESSMENT_VIEW,
} from '../../../../utils/routeConstant';
import AvatarFrame from '../../../common/Avatar';
import { convertDateToHumanView } from '../../../common/CommonHelperFunctions';
import { COMMON_ASSESSMENT_ENDPOINT } from '../../../common/api';
import AssessmentActivityDataTable from '../../Common/AssessmentActivityDataTable';

interface User {
  id: number;
  name: string;
}

interface Process {
  id: number;
  name: string;
}

interface AssessmentTemplate {
  id: number;
  name: string;
  key: string;
}

interface Assessment {
  id: number;
  type: string;
  assessment_name: string;
  key: string;
  AssessmentTemplates?: AssessmentTemplate[];
}

interface Department {
  id: number;
  name: string;
}

interface AssessmentTaskOverviewTable {
  id: number;
  assessment_id: number;
  risks: string | null;
  progress: number | null;
  start_date: string | null;
  end_date: string | null;
  tentative_date: string | null;
  status: string;
  Assessment: Assessment;
  Department: Department | null;
  Process?: Process | null;
  isCollaborator?: boolean;
  isAssigned?: boolean;
  AssignedTo: User;
  Approver: User;
  Owner: User;
  Group: Department;
}

interface AssessmentsTabProps {
  onSearch?: (query: string) => void;
  onFilterStatus?: (status: string) => void;
  onFilterType?: (type: string) => void;
  onView?: (id: number) => void;
  onEdit?: (id: number) => void;
  onComment?: (id: number) => void;
}

export function AssessmentsTab({ onSearch, onFilterStatus, onFilterType }: AssessmentsTabProps) {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const auth = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('filter');

  const { rowData, loading, selectedEntityId, user_type } = useSelector(
    (state: RootState) => state.assessmentTaskOverview
  );
  const loginData = useSelector((state: any) => state.auth.login.login_details);

  // Helper functions for status badge
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'Under Review':
        return <Clock className="h-4 w-4" />;
      case 'Started':
      case 'In Progress':
        return <PlayCircle className="h-4 w-4" />;
      case 'Change Request':
        return <XCircle className="h-4 w-4" />;
      case 'Yet to Start':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'default';
      case 'Under Review':
        return 'secondary';
      case 'Started':
      case 'In Progress':
        return 'outline';
      case 'Change Request':
        return 'destructive';
      case 'Yet to Start':
        return 'outline';
      default:
        return 'outline';
    }
  };

  // Column definitions for the table
  const assessmentColumns: ColumnDef<AssessmentTaskOverviewTable>[] = [
    {
      accessorKey: 'Assessment',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Assessment Name
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const assessment: Assessment = row.getValue('Assessment');
        return (
          <div className="font-medium">{`${
            assessment && assessment?.assessment_name ? assessment?.assessment_name : '-'
          }`}</div>
        );
      },
    },
    {
      accessorKey: 'Key',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Type
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const assessment: Assessment = row.getValue('Assessment');
        return (
          <Badge variant="outline">
            <div className="font-medium">{`${
              assessment && assessment?.key ? assessment?.key : '-'
            }`}</div>
          </Badge>
        );
      },
    },
    {
      accessorKey: 'AssignedTo',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Assigned To
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const assignedTo: User = row.getValue('AssignedTo');
        return assignedTo?.name ? (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap">
            <AvatarFrame value={`${assignedTo?.name}`} getInitials={getInitialsByName} />
            {assignedTo?.name}
          </div>
        ) : (
          <div>-</div>
        );
      },
    },
    {
      accessorKey: 'Approver',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Reviewer
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const reviewer: User = row.getValue('Approver');
        return reviewer?.name ? (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap">
            <AvatarFrame value={`${reviewer?.name}`} getInitials={getInitialsByName} />
            {reviewer?.name}
          </div>
        ) : (
          <div>-</div>
        );
      },
    },
    {
      accessorKey: 'tentative_date',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Due Date
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const tentative_date: string = row.getValue('tentative_date');
        return (
          <div className="">{tentative_date ? convertDateToHumanView(tentative_date) : '-'}</div>
        );
      },
    },
    {
      accessorKey: 'progress',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Progress
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const progress = Number(row.getValue('progress'));
        return (
          <div className={`flex flex-col justify-between gap-2`}>
            {progress || progress === 0 ? (
              <>
                {progress}%
                <Progress value={progress} className="h-2 w-[100px]" color="bg-custom-primary" />
              </>
            ) : (
              '-'
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Status
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return (
          <Badge variant={getStatusVariant(status)} className="flex w-fit items-center gap-1">
            {getStatusIcon(status)}
            {status || 'NA'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'Preview',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Preview
          </Button>
        );
      },
      cell: ({ row }) => {
        const status: string = row.getValue('status');
        const assessmentId: number = row?.original?.id;
        let isPreviewDisabled: boolean = true;
        if (
          (status === 'Under Review' &&
            !row?.original?.isCollaborator &&
            !row?.original?.isAssigned) ||
          (status === 'Under Review' && user_type === 'DPO')
        ) {
          isPreviewDisabled = false;
        }

        return (
          <Button
            variant="default"
            onClick={(event) => {
              if (isPreviewDisabled) {
                event?.stopPropagation();
              } else {
                handleReviewNavigation(
                  event,
                  row?.original?.isCollaborator || false,
                  'department',
                  assessmentId,
                  row?.original?.Assessment,
                  row?.original
                );
              }
            }}
            className={`${isPreviewDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
          >
            <ScanEye></ScanEye>
          </Button>
        );
      },
    },
    {
      accessorKey: 'action',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="w-full p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Action
          </Button>
        );
      },
      cell: ({ row }) => {
        const isDisabled =
          row?.original?.status === 'Under Review' || row?.original?.status === 'Completed';

        const assessmentId: number = row?.original?.id;
        const isDisabledDownload = row?.original?.status !== 'Completed';

        return isDisabledDownload ? (
          <div className="flex flex-row gap-2">
            <Button
              variant="default"
              onClick={(event) => {
                if (isDisabled) {
                  event?.stopPropagation();
                } else {
                  handleNavigation(
                    event,
                    row?.original?.isCollaborator || false,
                    'department',
                    assessmentId,
                    row?.original?.Assessment,
                    row?.original,
                    row?.original?.status
                  );
                  dispatch(riskAssessmentActions.setAssessmentStatus(row.original?.status));
                }
              }}
              className={`mx-2 h-8 w-16 justify-center whitespace-nowrap rounded-lg border border-solid border-indigo-600 px-4 py-1 font-['Poppins'] text-xs font-medium text-indigo-600 ${
                isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
              }`}
            >
              Start
            </Button>
          </div>
        ) : (
          <div className="flex w-full flex-row items-center gap-2">
            <Button
              variant="default"
              onClick={(event) => {
                if (isDisabledDownload) {
                  event?.stopPropagation();
                } else {
                  handleDownload(event, assessmentId);
                }
              }}
              className={`w-8 p-0 ${isDisabledDownload ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
            >
              <img src={assessmentDownload} alt="Download" />
            </Button>
            {row?.original?.status === 'Completed' && (
              <Button
                variant="default"
                onClick={(event) => {
                  handleNavigation(
                    event,
                    row?.original?.isCollaborator || false,
                    'department',
                    assessmentId,
                    row?.original?.Assessment,
                    row?.original,
                    row?.original?.status
                  );
                  dispatch(riskAssessmentActions.setAssessmentStatus(row.original?.status));
                }}
                className={`h-8 w-16 cursor-pointer font-['Poppins'] text-xs font-medium text-indigo-600`}
              >
                <img className="size-5 text-indigo-600" src={solidEye} alt="eye icon" />
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  // Handler functions
  const handleDownload = async (event: any, id: number) => {
    event.stopPropagation();
    try {
      const url = `${COMMON_ASSESSMENT_ENDPOINT}/download-controls/${id}`;
      toast.loading('Processing...');
      const response = await downloadAssessment(url);
      if (response.success) {
        toast.dismiss();
        toast.success('Download completed successfully');
      }
    } catch (error: any) {
      console.log(error);
      toast.dismiss();
      toast.error(error?.response?.data?.message);
    }
  };

  const handleNavigation = async (
    event: any,
    isCollaborator: boolean,
    path: string,
    id: number,
    assessment: Assessment,
    item: any,
    status?: string
  ) => {
    event.stopPropagation();
    dispatch(riskAssessmentActions.setSelectedRowDetails(item));
    if (status === 'Completed') {
      dispatch(
        riskAssessmentActions.setSelectedLabelData({
          id,
          path,
          assessment,
          category_id: -1,
          isCollaborator,
        })
      );
      const encryptedId = encryptId(id.toString());
      const targetPath = `/assessment-management/task-overview/controls/${encryptedId}`;
      navigate(targetPath, {
        state: {
          id,
          path,
          assessment,
          isCollaborator,
        },
      });
    } else {
      try {
        toast.loading('Processing...');
        const response = await axios.get(`${COMMON_ASSESSMENT_ENDPOINT}/start/${id}`, {
          headers: {
            'x-api-key': api_key,
            Authorization: 'Bearer ' + (auth?.user?.access_token || loginData?.access_token),
          },
        });
        if (response.status !== 200) {
          return response;
        }
        const data = response?.data || null;
        toast.dismiss();
        toast.success(data?.message);
        dispatch(
          riskAssessmentActions.setSelectedLabelData({
            id,
            path,
            assessment,
            category_id: -1,
            isCollaborator,
          })
        );
        const encryptedId = encryptId(id.toString());
        const targetPath = `/assessment-management/task-overview/controls/${encryptedId}`;
        navigate(targetPath, {
          state: {
            id,
            path,
            assessment,
            isCollaborator,
          },
        });
      } catch (error: any) {
        if (error) {
          console.log('Fetching Assessment Basic Info data failed!', error);
          toast.dismiss();
          toast.error(error?.response?.data?.message);
        }
      }
    }
  };

  const handleReviewNavigation = async (
    event: any,
    isCollaborator: boolean,
    path: string,
    id: number,
    assessment: Assessment,
    item: any
  ) => {
    event.stopPropagation();
    dispatch(riskAssessmentActions.setSelectedRowDetails(item));
    navigate(`${ASSESSMENT_REVIEW}`);
    dispatch(
      riskAssessmentActions.setSelectedLabelData({
        id,
        path,
        assessment,
        category_id: -1,
        isCollaborator,
      })
    );
  };

  const handleAssessmentView = (event: any, data: any, key?: string) => {
    event.stopPropagation();
    dispatch(riskAssessmentActions.setSelectedRowDetails(data));
    navigate(`${ASSESSMENT_VIEW}`);
  };

  useEffect(() => {
    if (loginData?.customer_id && selectedEntityId !== -1) {
      dispatch(fetchAssessmentTaskOverviewTable(selectedEntityId, searchTerm, selectedFilter));
    }
  }, [dispatch, loginData?.customer_id, selectedEntityId, searchTerm, selectedFilter]);

  useEffect(() => {
    if (loginData?.customer_id) {
      dispatch(fetchEntities(loginData.customer_id, selectedEntityId));
    }
  }, [dispatch, loginData?.customer_id, selectedEntityId]);

  return (
    <div>
      <Card className="shadow-md">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            {' '}
            <CardTitle>Active Assessments</CardTitle>
            <CardDescription>Monitor assessment progress and manage assignments</CardDescription>
          </div>

          <div>
            <Button
              variant="outline"
              className="w-fit"
              onClick={() => navigate(ASSESSMENT_MANAGEMENT_TASK_OVERVIEW)}
            >
              {t('AssessmentManagement.Dashboard.ViewAll')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <AssessmentActivityDataTable
            data={rowData}
            columns={assessmentColumns}
            loading={loading}
            handleAssessmentView={handleAssessmentView}
            handleModalOpen={() => {}} // Not used in this context
            handleNavigation={handleNavigation}
            handleReviewNavigation={handleReviewNavigation}
          />
        </CardContent>
      </Card>
    </div>
  );
}
