import { Settings } from 'lucide-react';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import { Badge } from '../../../../@/components/ui/badge';

interface Workflow {
  name: string;
  description: string;
  status: string;
}

interface WorkflowStat {
  value: string;
  label: string;
  color: string;
}

interface WorkflowsTabProps {
  workflows?: Workflow[];
  stats?: WorkflowStat[];
  onConfigureWorkflow?: (workflowName: string) => void;
}

export function WorkflowsTab({
  workflows = [],
  stats = [],
  onConfigureWorkflow,
}: WorkflowsTabProps) {
  const defaultWorkflows: Workflow[] = [
    {
      name: 'Standard PIA Workflow',
      description: 'Assignment → Completion → Review → Approval',
      status: 'Active',
    },
    {
      name: 'Vendor Assessment Workflow',
      description: 'Due Diligence → Risk Assessment → Approval',
      status: 'Active',
    },
  ];

  const defaultStats: WorkflowStat[] = [
    { value: '87%', label: 'On-time Completion', color: 'text-blue-600' },
    { value: '12', label: 'Avg Days to Complete', color: 'text-green-600' },
    { value: '3.2', label: 'Avg Review Cycles', color: 'text-purple-600' },
    { value: '94%', label: 'Approval Rate', color: 'text-orange-600' },
  ];

  const displayWorkflows = workflows.length > 0 ? workflows : defaultWorkflows;
  const displayStats = stats.length > 0 ? stats : defaultStats;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Assessment Workflows</CardTitle>
        <CardDescription>Automated and manual workflow management</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="space-y-4">
            <h3 className="font-medium">Active Workflows</h3>
            <div className="space-y-3">
              {displayWorkflows.map((workflow, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg border p-4"
                >
                  <div>
                    <h4 className="font-medium">{workflow.name}</h4>
                    <p className="text-sm text-gray-600">{workflow.description}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">{workflow.status}</Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onConfigureWorkflow?.(workflow.name)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="font-medium">Workflow Statistics</h3>
            <div className="grid grid-cols-2 gap-4">
              {displayStats.map((stat, index) => (
                <div key={index} className="rounded-lg border p-4 text-center">
                  <div className={`text-2xl font-bold ${stat.color}`}>{stat.value}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
