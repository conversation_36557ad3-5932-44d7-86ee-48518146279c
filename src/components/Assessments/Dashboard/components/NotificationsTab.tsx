import { Eye, Trash2 } from 'lucide-react';
import { But<PERSON> } from '../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';

interface Notification {
  type: string;
  message: string;
  priority: string;
  time: string;
}

interface NotificationsTabProps {
  notifications?: Notification[];
  onView?: (index: number) => void;
  onDelete?: (index: number) => void;
}

export function NotificationsTab({ notifications = [], onView, onDelete }: NotificationsTabProps) {
  const defaultNotifications: Notification[] = [
    {
      type: 'deadline',
      message: 'Privacy Impact Assessment due in 2 days',
      priority: 'high',
      time: '1 hour ago',
    },
    {
      type: 'review',
      message: 'Vendor Risk Assessment ready for review',
      priority: 'medium',
      time: '3 hours ago',
    },
    {
      type: 'comment',
      message: 'New comment on AI Compliance Assessment',
      priority: 'low',
      time: '5 hours ago',
    },
    {
      type: 'assignment',
      message: 'New assessment assigned to you',
      priority: 'high',
      time: '1 day ago',
    },
  ];

  const displayNotifications = notifications.length > 0 ? notifications : defaultNotifications;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      default:
        return 'bg-green-500';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Center</CardTitle>
        <CardDescription>Stay updated with assessment activities and deadlines</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displayNotifications.map((notification, index) => (
            <div key={index} className="flex items-start space-x-4 rounded-lg border p-4">
              <div
                className={`mt-2 h-2 w-2 rounded-full ${getPriorityColor(notification.priority)}`}
              />
              <div className="flex-1">
                <p className="text-sm font-medium">{notification.message}</p>
                <p className="mt-1 text-xs text-gray-500">{notification.time}</p>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={() => onView?.(index)}>
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={() => onDelete?.(index)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
