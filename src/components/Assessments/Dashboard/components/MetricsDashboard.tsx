import { useQuery } from '@tanstack/react-query';
import {
  <PERSON>ert<PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  Database,
  FileText,
  Loader2,
  Users,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import { fetchAssessmentStats } from '../../../common/services/privacyAssessment';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ReactNode;
  valueColor?: string;
  loading?: boolean;
}

interface AssessmentStatusData {
  assessment: string;
  count: string;
}

function MetricCard({
  title,
  value,
  subtitle,
  icon,
  valueColor = 'text-2xl font-bold',
  loading = false,
}: MetricCardProps) {
  return (
    <Card className="shadow-md">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className={valueColor}>
          {loading ? <Loader2 className="h-6 w-6 animate-spin" /> : value}
        </div>
        <p className="text-xs text-slate-600">{subtitle}</p>
      </CardContent>
    </Card>
  );
}

export function MetricsDashboard() {
  const { data: assessmentData = [], isLoading: loading } = useQuery<AssessmentStatusData[]>({
    queryKey: ['assessmentStats'],
    queryFn: async () => {
      const response = await fetchAssessmentStats();
      return response?.data?.result || [];
    },
    refetchOnWindowFocus: false,
  });

  const getAssessmentCount = (type: string) => {
    const item = assessmentData.find((item: AssessmentStatusData) => item.assessment === type);
    return item ? Number(item.count) : 0;
  };

  const totalAssessments = getAssessmentCount('TOTAL');
  const inProgressAssessments = getAssessmentCount('IN_PROGRESS');
  const completedAssessments = getAssessmentCount('COMPLETED');
  const yetToStartAssessments = getAssessmentCount('YET_TO_START');
  const templateCount = getAssessmentCount('TEMPLATE_TOTAL');
  const totalCollaborator = getAssessmentCount('COLLABORATOR_TOTAL');

  const metrics = [
    {
      title: 'Total Assessments',
      value: totalAssessments,
      subtitle: `${yetToStartAssessments} yet to start`,
      icon: <FileText className="h-4 w-4 text-slate-400" />,
    },
    {
      title: 'Yet To Start',
      value: yetToStartAssessments,
      subtitle: 'Require attention',
      icon: <AlertTriangle className="h-4 w-4 text-red-400" />,
      valueColor: 'text-2xl font-bold',
    },
    {
      title: 'In Progress',
      value: inProgressAssessments,
      subtitle: 'Currently active',
      icon: <Clock className="h-4 w-4 text-blue-500" />,
    },
    {
      title: 'Completed',
      value: completedAssessments,
      subtitle: 'This period',
      icon: <CheckCircle className="h-4 w-4 text-green-500" />,
    },

    {
      title: 'Templates',
      value: templateCount,
      subtitle: 'Available',
      icon: <Database className="h-4 w-4 text-slate-400" />,
    },
    {
      title: 'Collaborators',
      value: totalCollaborator,
      subtitle: 'Active users',
      icon: <Users className="h-4 w-4 text-slate-400" />,
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-6">
      {metrics.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          subtitle={metric.subtitle}
          icon={metric.icon}
          valueColor={metric.valueColor}
          loading={loading}
        />
      ))}
    </div>
  );
}
