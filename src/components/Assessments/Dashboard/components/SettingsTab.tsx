import { Badge } from '../../../../@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import { Switch } from '../../../../@/components/ui/switch';

interface ConfigSetting {
  name: string;
  enabled: boolean;
}

interface Integration {
  name: string;
  description: string;
  status: string;
}

interface SettingsTabProps {
  configSettings?: ConfigSetting[];
  integrations?: Integration[];
  onToggleSetting?: (settingName: string, enabled: boolean) => void;
}

export function SettingsTab({
  configSettings = [],
  integrations = [],
  onToggleSetting,
}: SettingsTabProps) {
  const defaultConfigSettings: ConfigSetting[] = [
    { name: 'Auto-assignment', enabled: false },
    { name: 'Email notifications', enabled: true },
    { name: 'Risk scoring', enabled: true },
    { name: 'Collaboration features', enabled: true },
  ];

  const defaultIntegrations: Integration[] = [
    {
      name: 'SIEM Integration',
      description: 'Security Information Event Management',
      status: 'Connected',
    },
    {
      name: 'GRC Platform',
      description: 'Governance, Risk & Compliance',
      status: 'Disconnected',
    },
  ];

  const displayConfigSettings = configSettings.length > 0 ? configSettings : defaultConfigSettings;
  const displayIntegrations = integrations.length > 0 ? integrations : defaultIntegrations;

  const getIntegrationVariant = (status: string) => {
    return status === 'Connected' ? 'secondary' : 'outline';
  };

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Assessment Configuration</CardTitle>
          <CardDescription>Configure assessment types and categories</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {displayConfigSettings.map((setting, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm font-medium">{setting.name}</span>
                <Switch
                  checked={setting.enabled}
                  onCheckedChange={(checked) => onToggleSetting?.(setting.name, checked)}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Integration Settings</CardTitle>
          <CardDescription>Configure external system integrations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {displayIntegrations.map((integration, index) => (
              <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                <div>
                  <h4 className="text-sm font-medium">{integration.name}</h4>
                  <p className="text-xs text-gray-600">{integration.description}</p>
                </div>
                <Badge variant={getIntegrationVariant(integration.status)}>
                  {integration.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
