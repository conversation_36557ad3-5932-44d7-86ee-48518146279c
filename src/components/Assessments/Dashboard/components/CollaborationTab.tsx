import { MessageSquare, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Badge } from '../../../../@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import { Progress } from '../../../../@/components/ui/Common/Elements/ProgressBar/ProgressBar';
import { fetchAssessmentCollaboratorDashboard } from '../../../common/services/assessment';

interface Assessment {
  assessment_id: number;
  assessment_name: string;
  status: string;
}

interface Collaborator {
  assignee_id: number;
  assignee_name: string;
  total_assessment: number;
  completed_assessment: number;
  completion_percentage: number;
  assessment: Assessment[];
}

interface CollaborationTabProps {
  onMessage?: (assessment: string) => void;
  onAddUser?: (assessment: string) => void;
}

export function CollaborationTab({ onMessage, onAddUser }: CollaborationTabProps) {
  const { t } = useTranslation();
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const response = await fetchAssessmentCollaboratorDashboard('');
        if (response?.data?.success) {
          setCollaborators(response.data.result);
        }
      } catch (err) {
        console.error('Error:', err);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  // const getStatusVariant = (status: string) => {
  //   switch (status.toLowerCase()) {
  //     case 'completed':
  //       return 'default';
  //     case 'under review':
  //       return 'secondary';
  //     case 'started':
  //       return 'outline';
  //     case 'yet to start':
  //       return 'destructive';
  //     default:
  //       return 'outline';
  //   }
  // };

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1">
        <Card className="flex h-[450px] flex-col overflow-auto shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              {t('Active Collaborations')}
            </CardTitle>
            <CardDescription>
              {t('Real-time collaboration on assessments and reviews')}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-1 flex-col space-y-4 overflow-hidden">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading...</p>
              </div>
            ) : collaborators.length === 0 ? (
              <div className="py-8 text-center">
                <MessageSquare className="mx-auto mb-4 h-12 w-12 text-slate-300" />
                <p className="text-slate-500">{t('No active collaborations found')}</p>
              </div>
            ) : (
              <div className="flex-1 space-y-4 overflow-y-auto">
                {collaborators.map((collaborator) => (
                  <div key={collaborator.assignee_id} className="space-y-3 rounded-lg border p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{collaborator.assignee_name}</h4>
                        <p className="text-sm text-slate-600">
                          {collaborator.total_assessment} {t('assessments')}
                        </p>
                      </div>
                      <Badge variant="outline">Collaborator</Badge>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{t('Completion')}</span>
                        <span>{collaborator.completion_percentage.toFixed(0)}%</span>
                      </div>
                      <Progress
                        value={collaborator.completion_percentage}
                        color="bg-custom-primary"
                        className="h-2"
                      />
                    </div>

                    <div className="space-y-2">
                      <p className="text-sm font-medium">{t('Assessments')}:</p>
                      <div className="flex flex-wrap gap-2">
                        {collaborator.assessment.slice(0, 3).map((assessment) => (
                          <Badge
                            key={assessment.assessment_id}
                            variant="outline"
                            className="whitespace-nowrap"
                          >
                            {assessment.assessment_name}
                          </Badge>
                        ))}
                        {collaborator.assessment.length > 3 && (
                          <Badge variant="outline" className="whitespace-nowrap">
                            +{collaborator.assessment.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
