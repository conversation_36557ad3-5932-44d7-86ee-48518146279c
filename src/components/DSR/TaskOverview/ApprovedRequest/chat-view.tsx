import { format } from 'date-fns';
import { Maximize2 } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
import { Card } from '../../../../@/components/ui/Common/Elements/Card/Card';
import { Avatar, AvatarFallback } from '../../../../@/components/ui/avatar';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '../../../../@/components/ui/sheet';
import { fetchChatActivities } from '../../../common/services/data-subject-request';
import ExpandablePanel from './ExpandableLog';
import MailTemplateModal from './mail-template-modal';

interface User {
  first_name: string;
  last_name: string;
}

interface DsrMailDocument {
  url: string;
  original_name: string;
}

interface Activity {
  first_name: string;
  last_name: string;
  content: string;
  subject: string;
  DsrMailDocuments: DsrMailDocument[];
  createdAt: string;
  unreadCount: number;
}

interface ChatViewProps {
  showChatView: boolean;
  setShowChatView: (show: boolean) => void;
  unreadCount: number;
  setUnreadCount: (count: number) => void;
  onChatClick: () => void;
}

const ChatView: React.FC<ChatViewProps> = ({
  showChatView,
  setShowChatView,
  unreadCount,
  setUnreadCount,
}) => {
  const [showMailModal, setShowMailModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [activityData, setActivityData] = useState<Activity[]>([]);
  const location = useLocation();
  const { data } = location?.state || {};
  const id = data?.id;
  const [isExpanded, setIsExpanded] = useState(false);

  const handleMailModalOpen = () => {
    setShowMailModal(true);
    setShowChatView(false);
  };

  const handleMailModalClose = () => {
    setShowMailModal(false);
    setShowChatView(true);
  };

  useEffect(() => {
    const getData = async () => {
      try {
        const data = await fetchChatActivities(id);
        setActivityData(data?.result);
        setUnreadCount(data?.unreadCount);
      } catch (error) {
        console.error('Error fetching chat activities:', error);
      }
    };

    getData();
  }, [showMailModal, id]);

  const ActivityTimeline = ({ isExpanded }: { isExpanded: boolean }) => (
    <div className="relative space-y-8">
      <div className="absolute left-[7px] top-0 mt-2 h-full w-0.5 bg-[#0043CE]"></div>

      {activityData?.map((activity, index) => (
        <div key={index} className="relative pt-2">
          <Card className="ml-6 rounded-lg border bg-white p-3">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-[#7B86EB] text-sm text-white">
                  {`${activity?.first_name?.charAt(0).toUpperCase() || ''}${activity?.last_name ? activity.last_name.charAt(0).toUpperCase() : ''}`}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-semibold">
                  {`${activity?.first_name} ${activity?.last_name}`}
                </p>
                <p className="text-xs text-gray-500">{activity?.subject}</p>
              </div>
            </div>

            {/* {isExpanded && activity?.content && ( */}
            <div className="m-4">
              <p
                className="text-sm text-gray-700"
                dangerouslySetInnerHTML={{ __html: activity?.content }}
              ></p>
            </div>
            {/* )} */}

            {activity?.DsrMailDocuments && activity?.DsrMailDocuments?.length > 0 && (
              <div className="mt-3">
                <p className="text-xs font-semibold">Documents:</p>
                <ul className="list-disc pl-5">
                  {activity?.DsrMailDocuments.map((doc, docIndex) => (
                    <li key={docIndex} className="text-xs text-blue-500 hover:underline">
                      <a href={doc?.url} target="_blank" rel="noopener noreferrer">
                        {doc?.original_name}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </Card>

          <div className="absolute left-0 top-full mt-2 flex items-center">
            <div className="flex h-4 w-4 items-center justify-center rounded-full border border-[#0043CE] bg-white p-[2px]">
              <div className="h-3 w-3 rounded-full bg-[#0043CE]"></div>
            </div>
            <span className="ml-2 text-xs text-gray-400">
              {format(new Date(activity?.createdAt), 'MMM dd yyyy hh:mm a')}
            </span>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <>
      {!isExpanded && (
        <Sheet open={showChatView} onOpenChange={setShowChatView}>
          <SheetContent className="flex h-full flex-col gap-1 px-0 py-2">
            <SheetHeader className="mt-4 flex flex-row items-center justify-between p-2">
              <SheetTitle className="">Activity Log</SheetTitle>
              <Button onClick={() => setIsExpanded(true)}>
                <Maximize2 />
              </Button>
            </SheetHeader>

            <hr className="mb-0 h-0.5 bg-slate-300" />

            <div className="relative flex-grow overflow-y-scroll px-4">
              <ActivityTimeline isExpanded={false} />
            </div>

            <SheetFooter className="w-full border-t bg-white p-4">
              <Button
                className="w-full bg-custom-primary text-white hover:bg-custom-primary"
                onClick={handleMailModalOpen}
              >
                <span className="flex items-center justify-center gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                  Mail Templates
                </span>
              </Button>
            </SheetFooter>
          </SheetContent>
        </Sheet>
      )}

      <ExpandablePanel
        isOpen={isExpanded}
        onClose={() => setIsExpanded(false)}
        title="Activity Log Details"
      >
        <div className="flex flex-col items-center gap-4">
          <div className="max-h-[75vh] min-w-[75vw] overflow-y-auto rounded-lg border p-8 scrollbar-hide">
            {activityData?.length > 0 ? (
              <ActivityTimeline isExpanded={true} />
            ) : (
              <div className="py-8 text-center text-gray-500">No activity available.</div>
            )}
          </div>

          <div className="fixed bottom-0 left-0 right-0 p-4">
            <div className="flex justify-end gap-4">
              <Button
                className="min-w-[150px] bg-custom-primary text-white hover:bg-custom-primary"
                onClick={handleMailModalOpen}
              >
                <span className="flex items-center justify-center gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                  Mail Templates
                </span>
              </Button>
            </div>
          </div>
        </div>
      </ExpandablePanel>
      <MailTemplateModal isOpen={showMailModal} onClose={handleMailModalClose} />
    </>
  );
};

export default ChatView;
