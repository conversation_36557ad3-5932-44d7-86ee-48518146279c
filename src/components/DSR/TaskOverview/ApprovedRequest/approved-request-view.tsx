import { AlertDialog } from '@radix-ui/react-alert-dialog';
import { ArchiveRestore, Maximize2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../../../../@/components/ui/AlertDialog';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../../../../@/components/ui/collapsible';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
  DialogTrigger,
} from '../../../../@/components/ui/Common/Elements/Dialog/Dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from '../../../../@/components/ui/Common/Elements/Form/Form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../@/components/ui/Common/Elements/Select/Select';
import { Switch } from '../../../../@/components/ui/switch';
import { Textarea } from '../../../../@/components/ui/textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../../../@/components/ui/tooltip';
import archive from '../../../../assets/archive.svg';
import downloadBlue from '../../../../assets/downloadBlue.svg';
import { fetchEntities } from '../../../../redux/actions/ActivityLog/ActivityLogActions';
import { AppDispatch, RootState } from '../../../../redux/store';
import { ApprovedRowData } from '../../../../types/data-subject-rights';
import { getInitialsByName } from '../../../../utils/helperData';
import {
  DSR_TASK_OVERVIEW,
  DSR_TASK_OVERVIEW_APPROVED,
  DSR_TASK_OVERVIEW_ARCHIVED,
  DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS,
  DSR_TASK_OVERVIEW_REJECTED,
} from '../../../../utils/routeConstant';
import { APPROVED_REQUEST_BY_ID, TASK_OVERVIEW } from '../../../common/api';
import AvatarFrame from '../../../common/Avatar';
import { convertDateToHumanView } from '../../../common/CommonHelperFunctions';
import { fetchDepartments } from '../../../common/services/assessment';
import {
  approveRejectedRequest,
  fetchAprovedReqeuestSteps,
  fetchStepWiseAuditLog,
  SaveAprovedReqeuestSteps,
  unarchiveRequest,
  updateBusinessUnit,
} from '../../../common/services/data-subject-request';
import { Step, Stepper, useStepper } from '../../../common/Stepper/DSR/index';
import IconWithModal from '../../Lab/Common/Modal/reject-reason-modal';
import ExpandablePanel from './ExpandableLog';
import Acknowledgment from './StepViews/acknowledgment';
import PreviewCreateRequestForm from './StepViews/preview-request-form';

interface Step {
  label: string;
  description: string;
}
interface StepRequest {
  step_title: string;
  id: string;
  order: number;
}

const ApprovedRequestView = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [approvedRequestDetails, setApprovedRequestDetail] = useState<ApprovedRowData>();
  const [approvedRequestDetailsData, setApprovedRequestDetailData] = useState<ApprovedRowData>();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const { data } = location.state;
  const [steps, setSteps] = useState<StepRequest[]>([]);
  const [activeSteps, setActiveStep] = useState<number>(() => data?.workflow_step_id);
  const { nextStep, setStep } = useStepper();
  const [id, seRequestId] = useState(location.state.data.id);
  const [isApproveDialogOpen, setApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [isRejectedDialogOpen, setRejectedDialogOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState('bg-custom-bg-danger text-custom-text-danger');
  const [selectedValue, setSelectedValue] = useState('rejected');
  const [openDialog, setOpenDialog] = useState(false);
  const userRole = useSelector((state: any) => state.auth.login.login_details);
  const navigate = useNavigate();
  const [stepAuditLogData, setStepAuditLogData] = useState(data.workflow_step_id);
  const [isOpen, setIsOpen] = useState(false);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [extendedDate, setExtendedDate] = useState<any>(data?.deadline);
  const [isExtended, setExtendedStatus] = useState(data?.extended == 'YES' ? true : false);
  const [switchChecked, setSwitchChecked] = useState(data?.extended === 'YES');
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [departments, setDepartments] = useState([]);
  const { isLastStep, activeStep } = useStepper();
  const [isArchiveDialogOpen, setArchiveDialogOpen] = useState(false);
  const [isUnArchiveDialogOpen, setUnArchiveDialogOpen] = useState(false);
  const { t } = useTranslation();

  const [selectedBusinessUnit, setSelectedBusinessUnit] = useState<string>(
    data?.business_unit ? String(data.business_unit) : ''
  );

  const { entities, selectedEntityId } = useSelector((state: RootState) => state.activityLog);
  let customer_id = 0;
  const loginData = useSelector((state: any) => state.auth.login.login_details);
  if (loginData !== null) {
    customer_id = loginData.customer_id;
  }

  const currentStep = data?.workflow_step_id;
  const stepsArray = steps.map((step) => step?.id);
  const currentStepIndex = currentStep ? stepsArray.indexOf(currentStep) : 0;

  const getData = async (activeStepId: number) => {
    // const workflow_step_id = approvedRequestDetails?.steps[activeStepIndex]?.id
    const url = activeStepId
      ? `${TASK_OVERVIEW}${APPROVED_REQUEST_BY_ID}/${id}?workflow_step_id=${activeStepId}&request_for=request`
      : `${TASK_OVERVIEW}${APPROVED_REQUEST_BY_ID}/${id}?&request_for=request`;
    const requestDetailsUrl = `${TASK_OVERVIEW}/${id}`;
    try {
      const requestdata = await fetchAprovedReqeuestSteps(url);
      const requestdataDetails = await fetchAprovedReqeuestSteps(requestDetailsUrl);
      const taskoverviewDetails = requestdata?.result;
      const stepData = taskoverviewDetails?.steps?.map((row: StepRequest, index: number) => {
        return {
          label: row?.step_title,
          // description:"",
          id: row?.id,
        };
      });
      if (requestdata?.status_code == 200 && taskoverviewDetails?.steps?.length > 0) {
        const stepId = activeStepId ? activeStepId : taskoverviewDetails?.steps[0]?.id;

        const auditUrl = `${TASK_OVERVIEW}/step-audit/${id}?request_for=request&step=${stepId}`;
        const stepAuditLog = await fetchStepWiseAuditLog(auditUrl);
        setStepAuditLogData(stepAuditLog);
      }

      setSteps(stepData);
      // setActiveStep(Number(approvedRequestDetailsData?.workflow_step_id))
      setApprovedRequestDetail(taskoverviewDetails);
      setApprovedRequestDetailData(requestdataDetails?.result);
    } catch (error) {
      console.error('Error setting chart data:', error);
    }
  };

  const handleTaskDataChangeStep = async (activeStepId: number) => {
    setActiveStep(activeStepId);
    await getData(activeStepId);
  };

  useEffect(() => {
    const initialStepId = data?.workflow_step_id;
    if (initialStepId) {
      getData(initialStepId);
    } else {
      getData(0);
    }
  }, []);

  const saveExtended = async (
    activeStepIndex: number,
    pathData: { extended: string; dsr_extended_timeline?: number }
  ) => {
    if (!approvedRequestDetails || !approvedRequestDetailsData) {
      console.error('Approved request details are not available.');
      return;
    }

    const url = `${TASK_OVERVIEW}/${approvedRequestDetails.id}`;
    try {
      const step = approvedRequestDetails.steps?.find((s) => s.id === activeStepIndex);

      if (!step) {
        console.error('Step is not available.');
        return;
      }

      const extendedDays = approvedRequestDetailsData?.regulations?.[0]?.dsr_extended_timeline;

      const payload = {
        ...pathData,
        ...(extendedDays !== null && { dsr_extended_timeline: extendedDays }),
      };

      const requestdata = await SaveAprovedReqeuestSteps(url, payload, step);
      if (requestdata?.success) {
        const updateDate = new Date(extendedDate);
        updateDate.setDate(updateDate.getDate() + extendedDays);
        setExtendedDate(updateDate);
        setExtendedStatus(true);
        toast.success(t('ToastMessages.General.DeadlineExtendedSuccessfully'));
      }
    } catch (error) {
      console.error('Error setting chart data:', error);
    }
  };

  const saveData = async (activeStepIndex: number, pathData: {}) => {
    if (approvedRequestDetails?.steps) {
      // const workflow_step_id = approvedRequestDetails?.steps?.length > 0 ? approvedRequestDetails?.steps[activeStep]?.id : undefined;
      const url = `${TASK_OVERVIEW}/${approvedRequestDetails.id}`;
      try {
        const step = approvedRequestDetails?.steps[activeStep];
        const requestdata = await SaveAprovedReqeuestSteps(url, pathData, step);
        if (requestdata?.success) {
          handleTaskDataChangeStep(approvedRequestDetails?.steps[activeStep + 1].id);
          // toast.success(t('ToastMessages.Forms.OperationSuccessful'));
        }
      } catch (error) {
        console.error('Error setting chart data:', error);
      }
    }
  };

  const handleRequestSubmit = async () => {
    try {
      setLoading(true);
      const reject_reason = form.getValues('reject_reason');
      const result = await approveRejectedRequest('APPROVED', id, reject_reason);
      if (result.success) {
        toast.success(t('ToastMessages.General.RequestApprovedSuccessfully'));
        if (!isLastStep) {
          if (approvedRequestDetails?.steps) {
            nextStep();
            const workflow_step_id =
              approvedRequestDetails?.steps?.length > 0
                ? approvedRequestDetails?.steps[activeStep + 1]?.id
                : undefined;
            const patchData = { workflow_step_id: workflow_step_id };
            saveData(activeStep, patchData);
          }
        }
        if (userRole?.role === 'Data Protection Officer') {
          navigate(DSR_TASK_OVERVIEW_APPROVED);
        } else {
          navigate(DSR_TASK_OVERVIEW);
        }
      }
    } catch (error) {
      console.error('Error approving request:', error);
    } finally {
      setLoading(false);
      setApproveDialogOpen(false);
      setOpenDialog(false);
    }
  };

  const handleRejectInProgressSubmit = async () => {
    try {
      setLoading(true);
      const reject_reason = form.getValues('reject_reason');
      const result = await approveRejectedRequest('REJECTED_IN_PROGRESS', id, reject_reason);

      if (result.success) {
        toast.success(t('ToastMessages.General.RequestRejectedSuccessfully'));
        if (userRole?.role === 'Data Protection Officer') {
          navigate(DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS);
        } else {
          navigate(DSR_TASK_OVERVIEW);
        }
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
      toast.error(t('ToastMessages.General.RejectMessageCannotBeEmpty'));
    } finally {
      setLoading(false);
      setRejectedDialogOpen(false);
      form.reset();
    }
  };

  const handleRejectSubmit = async () => {
    try {
      setLoading(true);
      const reject_reason = form.getValues('reject_reason');
      const result = await approveRejectedRequest('REJECTED', id, reject_reason);
      if (result.success) {
        toast.success(t('ToastMessages.General.RequestRejectedSuccessfully'));
        if (userRole?.role === 'Data Protection Officer') {
          navigate(DSR_TASK_OVERVIEW_REJECTED);
        } else {
          navigate(DSR_TASK_OVERVIEW);
        }
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
      toast.error(t('ToastMessages.General.RejectMessageCannotBeEmpty'));
      setRejectedDialogOpen(true);
    } finally {
      setLoading(false);
      // setRejectedDialogOpen(false);
      form.reset();
    }
  };

  const handleArchive = async () => {
    try {
      setLoading(true);
      const result = await unarchiveRequest('ARCHIVE', id);
      if (result.success) {
        toast.success(t('ToastMessages.General.RequestArchivedSuccessfully'));
        if (userRole?.role === 'Data Protection Officer') {
          navigate(DSR_TASK_OVERVIEW_ARCHIVED);
        } else {
          navigate(DSR_TASK_OVERVIEW);
        }
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
      toast.error(t('CommonErrorMessages.Error'));
    } finally {
      setLoading(false);
    }
  };

  const handleUnarchive = async () => {
    try {
      setLoading(true);
      const result = await unarchiveRequest('UNARCHIVE', id);
      if (result.success) {
        toast.success(t('ToastMessages.General.RequestUnarchivedSuccessfully'));
        if (userRole?.role === 'Data Protection Officer') {
          navigate(DSR_TASK_OVERVIEW_ARCHIVED);
        } else {
          navigate(DSR_TASK_OVERVIEW);
        }
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
      toast.error(t('CommonErrorMessages.Error'));
    } finally {
      setLoading(false);
    }
  };

  const handleDialogCancel = () => {
    setOpenDialog(false);
    setSelectedColor('bg-custom-bg-danger text-custom-text-danger');
    setSelectedValue('rejected');
  };
  const handleRejectCancel = () => {
    setRejectedDialogOpen(false);
    setSelectedColor('bg-custom-bg-danger text-custom-text-danger');
    setSelectedValue('rejected');
  };

  const handleOpenPanel = () => {
    setIsPanelOpen(true);
  };

  const handleClosePanel = () => {
    setIsPanelOpen(false);
  };

  const fullName =
    approvedRequestDetailsData?.User?.firstName || approvedRequestDetailsData?.User?.lastName
      ? `${approvedRequestDetailsData?.User?.firstName ?? ''} ${approvedRequestDetailsData?.User?.lastName ?? ''}`
      : '-';

  const handleDateExtend = async (value: boolean) => {
    if (value) {
      if (!approvedRequestDetails || !approvedRequestDetailsData) {
        console.error('Approved request details are not available.');
        return;
      }
      const extendedDays = approvedRequestDetailsData?.extended_days;
      await saveExtended(activeSteps, {
        extended: 'YES',
        ...(extendedDays !== null && { dsr_extended_timeline: extendedDays }),
      });
    }
  };

  const handleSwitchChange = (value: boolean) => {
    setSwitchChecked(value); // Update switch state
    if (value) {
      setShowConfirmationModal(true); // Show modal on switch toggle
    }
  };

  // Function to handle the confirmation in modal
  const confirmExtendDate = () => {
    setShowConfirmationModal(false);
    handleDateExtend(true);
  };

  // Function to cancel and reset switch to original state
  const cancelExtendDate = () => {
    setShowConfirmationModal(false);
    setSwitchChecked(false); // Reset switch to off
  };

  const form = useForm({
    defaultValues: {
      reject_reason: approvedRequestDetailsData?.reject_reason || '',
    },
  });

  const handleEntityChange = async (value: string) => {
    try {
      const entityId = parseInt(value, 10);

      if (data?.id && data?.business_unit !== entityId) {
        await updateBusinessUnit(data?.id, value);
        toast.success(t('ToastMessages.General.BusinessUnitUpdatedSuccessfully'));

        const updatedDataDetails = await fetchAprovedReqeuestSteps(`${TASK_OVERVIEW}/${data.id}`);
        if (updatedDataDetails?.result) {
          setApprovedRequestDetailData(updatedDataDetails?.result);
        }
      }

      const response = await fetchDepartments(entityId);
      if (response?.data?.result?.rows) {
        setDepartments(response.data.result.rows);
        console.log('Departments updated:', response.data.result.rows);
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
      setDepartments([]);
    }
  };

  useEffect(() => {
    if (data?.business_unit) {
      handleEntityChange(String(data.business_unit));
    }
  }, [data?.business_unit]);

  useEffect(() => {
    dispatch(fetchEntities(customer_id, selectedEntityId));
  }, [dispatch, customer_id]);

  return (
    <div className="size-full">
      {/* Expandable Panel Activity Log*/}
      <div>
        <ExpandablePanel isOpen={isPanelOpen} onClose={handleClosePanel} title="Form Preview">
          <div className="flex flex-col items-center">
            <div className="min-w-[75vw] rounded-md border border-gray-300">
              <PreviewCreateRequestForm />
            </div>
          </div>
        </ExpandablePanel>
      </div>
      {approvedRequestDetails && (
        <>
          <div className="flex flex-row justify-between py-3">
            <span className="text-xl font-semibold text-black">
              {`${t('DSR.TaskOverView.DataSubjectRequestDetailsID')} (${data?.dsr_id})`}
            </span>
            <div className="flex flex-row items-center justify-end gap-4">
              <div className="cursor-pointer rounded-md p-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button className="rounded-full focus:outline-none focus:ring-2 focus:ring-slate-100 focus:ring-offset-2">
                        <IconWithModal
                          data={approvedRequestDetailsData?.reject_reason}
                          status={approvedRequestDetailsData?.status}
                        />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent className="rounded-lg border-none bg-black bg-opacity-80 px-4 py-2 text-white">
                      <p>Rejection reason</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              {approvedRequestDetailsData?.status === 'PENDING' ? (
                <>
                  <Dialog open={isApproveDialogOpen} onOpenChange={setApproveDialogOpen}>
                    <DialogTrigger asChild>
                      <Button
                        type="button"
                        className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                      >
                        {t('DSR.TaskOverView.Approve')}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="mx-auto my-auto flex h-[180px] max-w-[30%] flex-col items-center justify-center text-center">
                      <DialogTitle className="mt-4 text-center">
                        {t('DSR.TaskOverView.ConfirmApprove')}
                      </DialogTitle>
                      <DialogDescription></DialogDescription>
                      <DialogFooter className="mt-4 flex items-center justify-center gap-4">
                        <Button
                          type="button"
                          className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                          onClick={() => setApproveDialogOpen(false)}
                        >
                          {t('Common.Cancel')}
                        </Button>
                        <Button
                          type="button"
                          className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                          onClick={handleRequestSubmit}
                        >
                          {t('DSR.TaskOverView.Approve')}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <Dialog open={isRejectDialogOpen} onOpenChange={setRejectDialogOpen}>
                    <DialogTrigger asChild>
                      <Button
                        type="button"
                        className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                      >
                        {t('DSR.TaskOverView.Reject')}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="flex min-h-[210px] flex-col">
                      <DialogTitle>{t('DSR.TaskOverView.AddNote')}</DialogTitle>
                      <DialogDescription className="my-3 w-full">
                        <Form {...form}>
                          <form className="space-y-4">
                            <FormField
                              control={form.control}
                              name="reject_reason"
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Textarea placeholder="Enter rejection note..." {...field} />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </form>
                        </Form>
                      </DialogDescription>
                      <DialogFooter className="flex justify-end">
                        <Button
                          type="button"
                          className="mr-2 w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                          onClick={() => setRejectDialogOpen(false)}
                        >
                          {t('Common.Cancel')}
                        </Button>
                        <Button
                          type="button"
                          className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                          onClick={handleRejectInProgressSubmit}
                        >
                          {t('DSR.TaskOverView.Reject')}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                  {/* Archive Button with Confirmation Dialog */}
                  {userRole.role === 'Data Protection Officer' ? (
                    <Dialog open={isArchiveDialogOpen} onOpenChange={setArchiveDialogOpen}>
                      <DialogTrigger asChild>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DialogTrigger asChild>
                                <Button className="rounded-md bg-[#E0E0E0] p-3 text-black">
                                  <img src={archive} className="h-5 w-5" />
                                </Button>
                              </DialogTrigger>
                            </TooltipTrigger>
                            <TooltipContent className="rounded-lg border-none bg-black bg-opacity-80 px-4 py-2 text-white">
                              <p>Archive</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </DialogTrigger>
                      <DialogContent className="mx-auto my-auto flex h-[180px] max-w-[30%] flex-col items-center justify-center text-center">
                        <DialogTitle className="mt-4 text-center">Confirm Archive</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to archive this request?
                        </DialogDescription>
                        <DialogFooter className="mt-4 flex items-center justify-center gap-4">
                          <Button
                            type="button"
                            className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                            onClick={() => setArchiveDialogOpen(false)}
                          >
                            {t('Common.Cancel')}
                          </Button>
                          <Button
                            type="button"
                            className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                            onClick={() => {
                              handleArchive();
                              setArchiveDialogOpen(false);
                            }}
                          >
                            Yes
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  ) : null}
                </>
              ) : approvedRequestDetailsData?.status === 'APPROVED' ? (
                <div className="flex gap-2">
                  <div className="rounded-md bg-custom-green p-2 font-semibold text-white">
                    {t('DSR.TaskOverView.APPROVED')}
                  </div>
                  {userRole.role === 'Data Protection Officer' ? (
                    <Dialog open={isArchiveDialogOpen} onOpenChange={setArchiveDialogOpen}>
                      <DialogTrigger asChild>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DialogTrigger asChild>
                                <Button className="rounded-md bg-[#E0E0E0] p-3 text-black">
                                  <img src={archive} className="h-5 w-5" />
                                </Button>
                              </DialogTrigger>
                            </TooltipTrigger>
                            <TooltipContent className="rounded-lg border-none bg-black bg-opacity-80 px-4 py-2 text-white">
                              <p>Archive</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </DialogTrigger>
                      <DialogContent className="mx-auto my-auto flex h-[180px] max-w-[30%] flex-col items-center justify-center text-center">
                        <DialogTitle className="mt-4 text-center">Confirm Archive</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to archive this request?
                        </DialogDescription>
                        <DialogFooter className="mt-4 flex items-center justify-center gap-4">
                          <Button
                            type="button"
                            className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                            onClick={() => setArchiveDialogOpen(false)}
                          >
                            {t('Common.Cancel')}
                          </Button>
                          <Button
                            type="button"
                            className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                            onClick={() => {
                              handleArchive();
                              setArchiveDialogOpen(false);
                            }}
                          >
                            Yes
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  ) : null}
                </div>
              ) : approvedRequestDetailsData?.status === 'COMPLETED' ? (
                <>
                  <div className="rounded-md bg-custom-green p-2 font-semibold text-white">
                    {t('DSR.TaskOverView.COMPLETED')}
                  </div>
                  {userRole.role === 'Data Protection Officer' ? (
                    <Dialog open={isArchiveDialogOpen} onOpenChange={setArchiveDialogOpen}>
                      <DialogTrigger asChild>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DialogTrigger asChild>
                                <Button className="rounded-md bg-[#E0E0E0] p-3 text-black">
                                  <img src={archive} className="h-5 w-5" />
                                </Button>
                              </DialogTrigger>
                            </TooltipTrigger>
                            <TooltipContent className="rounded-lg border-none bg-black bg-opacity-80 px-4 py-2 text-white">
                              <p>Archive</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </DialogTrigger>
                      <DialogContent className="mx-auto my-auto flex h-[180px] max-w-[30%] flex-col items-center justify-center text-center">
                        <DialogTitle className="mt-4 text-center">Confirm Archive</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to archive this request?
                        </DialogDescription>
                        <DialogFooter className="mt-4 flex items-center justify-center gap-4">
                          <Button
                            type="button"
                            className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                            onClick={() => setArchiveDialogOpen(false)}
                          >
                            {t('Common.Cancel')}
                          </Button>
                          <Button
                            type="button"
                            className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                            onClick={() => {
                              handleArchive();
                              setArchiveDialogOpen(false);
                            }}
                          >
                            Yes
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  ) : null}
                </>
              ) : approvedRequestDetailsData?.status === 'ARCHIVED' ? (
                <>
                  <Button className="rounded-md bg-[#E0E0E0] p-2 font-semibold text-black hover:bg-[#E0E0E0]">
                    ARCHIVED
                  </Button>
                  <Dialog open={isUnArchiveDialogOpen} onOpenChange={setUnArchiveDialogOpen}>
                    <DialogTrigger asChild>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <DialogTrigger asChild>
                              <Button className="rounded-md bg-[#E0E0E0] p-3 text-black">
                                <ArchiveRestore />
                              </Button>
                            </DialogTrigger>
                          </TooltipTrigger>
                          <TooltipContent className="rounded-lg border-none bg-black bg-opacity-80 px-4 py-2 text-white">
                            <p>Unarchive</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </DialogTrigger>
                    <DialogContent className="mx-auto my-auto flex h-[180px] max-w-[30%] flex-col items-center justify-center text-center">
                      <DialogTitle className="mt-4 text-center">Confirm Unarchive</DialogTitle>
                      <DialogDescription>
                        Are you sure you want to unarchive this request?
                      </DialogDescription>
                      <DialogFooter className="mt-4 flex items-center justify-center gap-4">
                        <Button
                          type="button"
                          className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                          onClick={() => setUnArchiveDialogOpen(false)}
                        >
                          {t('Common.Cancel')}
                        </Button>
                        <Button
                          type="button"
                          className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                          onClick={() => {
                            handleUnarchive();
                            setUnArchiveDialogOpen(false);
                          }}
                        >
                          Yes
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </>
              ) : approvedRequestDetailsData?.status === 'REJECTED' ? (
                <div className="flex gap-2">
                  <Select
                    value={selectedValue}
                    onValueChange={(value) => {
                      setSelectedValue(value);
                      if (value === 'rejected') {
                        setSelectedColor('bg-custom-bg-danger text-custom-text-danger');
                      } else {
                        setSelectedColor('bg-custom-green text-custom-darkGreen');
                        setOpenDialog(true);
                      }
                    }}
                  >
                    <SelectTrigger className={`h-10 w-[180px] ${selectedColor}`}>
                      <SelectValue placeholder="Select option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rejected">{t('DSR.TaskOverView.Rejected')}</SelectItem>
                      <SelectItem value="approved">{t('DSR.TaskOverView.Approved')}</SelectItem>
                    </SelectContent>
                  </Select>
                  {userRole.role === 'Data Protection Officer' ? (
                    <Dialog open={isArchiveDialogOpen} onOpenChange={setArchiveDialogOpen}>
                      <DialogTrigger asChild>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DialogTrigger asChild>
                                <Button className="rounded-md bg-[#E0E0E0] p-3 text-black">
                                  <img src={archive} className="h-5 w-5" />
                                </Button>
                              </DialogTrigger>
                            </TooltipTrigger>
                            <TooltipContent className="rounded-lg border-none bg-black bg-opacity-80 px-4 py-2 text-white">
                              <p>Archive</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </DialogTrigger>
                      <DialogContent className="mx-auto my-auto flex h-[180px] max-w-[30%] flex-col items-center justify-center text-center">
                        <DialogTitle className="mt-4 text-center">Confirm Archive</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to archive this request?
                        </DialogDescription>
                        <DialogFooter className="mt-4 flex items-center justify-center gap-4">
                          <Button
                            type="button"
                            className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                            onClick={() => setArchiveDialogOpen(false)}
                          >
                            {t('Common.Cancel')}
                          </Button>
                          <Button
                            type="button"
                            className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                            onClick={() => {
                              handleArchive();
                              setArchiveDialogOpen(false);
                            }}
                          >
                            Yes
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  ) : null}
                </div>
              ) : approvedRequestDetailsData?.status === 'REJECTED_IN_PROGRESS' ? (
                <div className="flex gap-2">
                  <Select
                    value={selectedValue}
                    onValueChange={(value) => {
                      setSelectedValue(value);
                      if (value === 'rejected') {
                        setSelectedColor('bg-custom-bg-danger text-custom-text-danger');
                        setRejectedDialogOpen(true);
                      }
                      if (value === 'rejectedfinal') {
                        setSelectedColor('bg-custom-bg-danger text-custom-text-danger');
                        setRejectedDialogOpen(true);
                      } else {
                        setSelectedColor('bg-custom-green text-custom-darkGreen');
                        setOpenDialog(true);
                      }
                    }}
                  >
                    <SelectTrigger className={`h-10 w-[200px] ${selectedColor}`}>
                      <SelectValue placeholder="Select option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rejected">
                        {t('DSR.TaskOverView.RejectionInProgress')}
                      </SelectItem>
                      <SelectItem value="rejectedfinal">{t('DSR.TaskOverView.Reject')}</SelectItem>
                      <SelectItem value="approved">{t('DSR.TaskOverView.Approved')}</SelectItem>
                    </SelectContent>
                  </Select>
                  {userRole.role === 'Data Protection Officer' ? (
                    <Dialog open={isArchiveDialogOpen} onOpenChange={setArchiveDialogOpen}>
                      <DialogTrigger asChild>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DialogTrigger asChild>
                                <Button className="rounded-md bg-[#E0E0E0] p-3 text-black">
                                  <img src={archive} className="h-5 w-5" />
                                </Button>
                              </DialogTrigger>
                            </TooltipTrigger>
                            <TooltipContent className="rounded-lg border-none bg-black bg-opacity-80 px-4 py-2 text-white">
                              <p>Archive</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </DialogTrigger>
                      <DialogContent className="mx-auto my-auto flex h-[180px] max-w-[30%] flex-col items-center justify-center text-center">
                        <DialogTitle className="mt-4 text-center">Confirm Archive</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to archive this request?
                        </DialogDescription>
                        <DialogFooter className="mt-4 flex items-center justify-center gap-4">
                          <Button
                            type="button"
                            className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                            onClick={() => setArchiveDialogOpen(false)}
                          >
                            {t('Common.Cancel')}
                          </Button>
                          <Button
                            type="button"
                            className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                            onClick={() => {
                              handleArchive();
                              setArchiveDialogOpen(false);
                            }}
                          >
                            Yes
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  ) : null}
                </div>
              ) : null}
            </div>
          </div>
          <div className="flex flex-col gap-1 bg-white p-4">
            <div className="flex w-full flex-row items-center justify-between">
              <span
                // className={`w-[${fullNameWidth}px] font-medium text-[27px] text-custom-primary whitespace-nowrap mr-4`}
                className={`mr-4 whitespace-nowrap text-[27px] font-medium text-custom-primary`}
              >
                {data?.full_name}
              </span>
              <hr
                className="h-[2px] flex-grow bg-[#E2E2E2]"
                // style={{
                //   width: `calc(100% - ${fullNameWidth}px)`,
                // }}
              />
            </div>
            <div className="flex w-full flex-row flex-wrap gap-2 p-2">
              <div className="flex w-[220px] flex-col gap-2">
                <p className="text-base text-[#9CA3AF]">{t('DSR.TaskOverView.RequestID')}</p>
                <p className="text-sm font-medium">{data?.dsr_id}</p>
              </div>
              <div className="flex w-[220px] flex-col gap-2">
                <p className="text-base text-[#9CA3AF]">{t('DSR.Dashboard.RequestTypes')}</p>
                <p className="text-sm font-medium">{data?.request_type}</p>
              </div>

              {approvedRequestDetailsData?.status !== 'APPROVED' ? (
                <div className="flex w-[220px] flex-col gap-2">
                  <p className="text-base text-[#9CA3AF]">{t('DSR.TaskOverView.CreatedOn')}</p>
                  <p className="text-sm font-medium">
                    {data?.request_date
                      ? convertDateToHumanView(data?.request_date.toString())
                      : '-'}
                  </p>
                </div>
              ) : (
                <div className="flex w-[220px] flex-col gap-2">
                  <p className="text-base text-[#9CA3AF]">{t('DSR.TaskOverView.RequestDate')}</p>
                  <p className="text-sm font-medium">
                    {data?.start_date ? convertDateToHumanView(data?.start_date.toString()) : '-'}
                  </p>
                </div>
              )}

              {approvedRequestDetailsData?.status === 'APPROVED' ? (
                <div className="flex w-[220px] flex-col gap-2">
                  <p className="text-base text-[#9CA3AF]">{t('DSR.TaskOverView.Extended')}</p>
                  <Switch
                    checked={switchChecked}
                    onCheckedChange={(value) => handleSwitchChange(value)}
                    disabled={isExtended}
                  />
                </div>
              ) : null}
              {/* Confirmation Modal */}
              <AlertDialog open={showConfirmationModal} onOpenChange={setShowConfirmationModal}>
                <AlertDialogContent className="mx-auto my-auto flex h-[180px] max-w-[30%] flex-col items-center justify-center text-center">
                  <AlertDialogHeader>
                    <AlertDialogTitle className="mt-4 text-center">
                      {t('DSR.TaskOverView.ConfirmExtend')}
                    </AlertDialogTitle>
                  </AlertDialogHeader>
                  <AlertDialogFooter className="mt-4 flex items-center justify-center gap-4">
                    <AlertDialogCancel
                      className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                      onClick={cancelExtendDate}
                    >
                      {t('Common.Cancel')}
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={confirmExtendDate}
                      className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                    >
                      {t('DSR.TaskOverView.Confirm')}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
              <div className="flex w-[220px] flex-col">
                <p className="text-base text-[#9CA3AF]">{t('DSR.TaskOverView.Approver')}</p>
                <div className="flex items-center">
                  <AvatarFrame value={fullName} getInitials={getInitialsByName} />
                  <p className="pl-1 text-sm font-medium">{fullName}</p>
                </div>
              </div>
              <div className="flex w-[220px] flex-col gap-2">
                <p className="text-base text-[#9CA3AF]">{t('DSR.TaskOverView.Deadline')}</p>
                <p className="text-sm font-medium">
                  {extendedDate ? convertDateToHumanView(extendedDate.toString()) : '-'}
                </p>
                {/* <p className="text-sm font-medium">{approvedRequestDetailsData?.deadline_date ? convertDateToHumanView(approvedRequestDetailsData?.deadline_date.toString()):""}</p> */}
              </div>
              <div className="flex w-[220px] flex-col gap-2">
                <p className="text-base text-[#9CA3AF]">{t('DSR.TaskOverView.BusinessUnit')}</p>
                <p className="text-sm font-medium">
                  {entities?.find((entity: any) => String(entity?.id) === selectedBusinessUnit)
                    ?.name || '-'}
                </p>
              </div>
              <div className="flex w-[220px] flex-col gap-2">
                <p className="text-base text-[#9CA3AF]">Regulations</p>
                <p className="text-sm font-medium">
                  {approvedRequestDetailsData?.regulations?.[0]?.authoritative_source
                    ? approvedRequestDetailsData?.regulations[0]?.authoritative_source
                    : '-'}{' '}
                </p>
              </div>
            </div>
            <div className="flex w-full flex-col gap-2">
              <p className="text-base text-[#9CA3AF]">{t('DSR.TaskOverView.Attachments')}</p>
              <div className="flex w-full flex-row flex-wrap gap-2">
                {approvedRequestDetails?.RequestDocuments?.length > 0 ? (
                  approvedRequestDetails?.RequestDocuments.map((document: any) => (
                    <a
                      key={document?.id}
                      href={document?.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex min-w-[100px] max-w-[200px] shrink-0 cursor-pointer flex-row items-center gap-3 overflow-hidden rounded-lg border border-blue-600 bg-[#F5FAFF] p-2 hover:bg-[#E5F0FF]"
                    >
                      <div className="flex w-full items-center gap-2">
                        <div className="truncate font-['Poppins'] text-base font-normal leading-[18px] tracking-tight text-zinc-800">
                          {document.original_name || 'View Attachment'}
                        </div>
                        <img
                          src={downloadBlue}
                          alt="download sign"
                          className="size-4 flex-shrink-0 cursor-pointer"
                        />
                      </div>
                    </a>
                  ))
                ) : (
                  <p className="text-sm text-[#9CA3AF]">
                    {t('DSR.TaskOverView.NoAttachmentsAvailable')}
                  </p>
                )}
              </div>
            </div>
            <Collapsible>
              <CollapsibleTrigger
                className="flex w-full items-center space-x-2 text-primary"
                onClick={() => setIsOpen(!isOpen)}
              >
                <hr
                  className="h-[2px] w-[90%] bg-[#E2E2E2]"
                  // style={{
                  //   width: `calc(100% - ${fullNameWidth}px)`,
                  // }}
                />
                <span>{isOpen ? 'Show Less' : 'Show More'}</span>
                <div className="bg-custom-primary text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className={`size-4 transition-transform duration-300 ${
                      isOpen ? 'rotate-180' : 'rotate-0'
                    }`}
                    viewBox="0 0 20 20"
                    fill="white"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 011.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="flex w-full flex-row flex-wrap gap-2 p-2">
                  <div className="flex w-[260px] flex-col gap-2">
                    <p className="text-base text-[#9CA3AF]"> {t('DSR.TaskOverView.WebForm')}</p>
                    <p className="text-sm font-medium"> {t('DSR.TaskOverView.DSAR')}</p>
                    <div className="flex items-center">
                      <p className="text-sm font-medium"> {t('DSR.TaskOverView.FormPreview')}</p>
                      <Maximize2
                        className="ml-2 h-4 w-4 cursor-pointer"
                        onClick={handleOpenPanel}
                      />
                    </div>
                  </div>
                  {/* <div className="flex w-[260px] flex-col gap-2">
                <p className="text-base text-[#9CA3AF]">Last Portal Access</p>
                <p className="text-sm font-medium">25-08-2024</p>
              </div> */}
                  <div className="flex w-[260px] flex-col gap-2">
                    <p className="text-base text-[#9CA3AF]">
                      {' '}
                      {t('DSR.TaskOverView.PreferredLanguage')}
                    </p>
                    <p className="text-sm font-medium">English</p>
                  </div>
                  <div className="flex w-[260px] flex-col gap-2">
                    <p className="text-base text-[#9CA3AF]"> {t('DSR.TaskOverView.Region')}</p>
                    <p className="text-sm font-medium">
                      {approvedRequestDetailsData?.DataSubject?.country?.country_name}
                    </p>
                  </div>
                  {/* <div className="flex flex-col gap-2 w-[260px]">
            <p className="text-[#9CA3AF] text-base font-semibold">Approver</p>
            <p className="text-sm font-medium text-[#00cba0]">OPEN</p>
          </div> */}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
          {steps?.length && (
            <Stepper
              variant="circle-alt"
              initialStep={currentStepIndex}
              steps={steps}
              // className="mt-2.5 p-0 w-full items-center"
              className="mt-2 p-4 sm:w-full md:w-full md:items-center lg:h-[100px] lg:w-full lg:items-center lg:rounded-lg lg:border lg:border-primary-border lg:bg-primary-background"
            >
              {approvedRequestDetails &&
                steps?.map((stepProperties: StepRequest, index: string | number) => {
                  return (
                    <Step key={stepProperties.step_title} {...stepProperties}>
                      <Acknowledgment
                        departments={departments}
                        dataDetails={approvedRequestDetailsData}
                        details={approvedRequestDetails}
                        auditLogData={stepAuditLogData}
                        handleTaskDataChangeStep={(activeStepIndex: number) =>
                          handleTaskDataChangeStep(activeStepIndex)
                        }
                      />
                    </Step>
                  );
                })}
              {/* <MyStepperFooter /> */}
            </Stepper>
          )}

          <AlertDialog open={openDialog} onOpenChange={setOpenDialog}>
            <AlertDialogTrigger asChild />
            <AlertDialogContent className="mx-auto my-auto flex h-[180px] max-w-[30%] flex-col items-center justify-center text-center">
              <AlertDialogHeader>
                <AlertDialogTitle className="mt-4 text-center">
                  {t('DSR.TaskOverView.ConfirmApprove')}
                </AlertDialogTitle>
              </AlertDialogHeader>
              <AlertDialogFooter className="mt-4 flex items-center justify-center gap-4">
                <Button
                  onClick={handleDialogCancel}
                  type="button"
                  className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                >
                  {t('Common.Cancel')}
                </Button>
                <Button
                  onClick={handleRequestSubmit}
                  type="button"
                  className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                >
                  {t('DSR.TaskOverView.Approve')}
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <AlertDialog open={isRejectedDialogOpen} onOpenChange={setRejectedDialogOpen}>
            <AlertDialogTrigger asChild />
            <AlertDialogContent className="flex min-h-[210px] flex-col">
              <AlertDialogHeader>
                <AlertDialogTitle>
                  {approvedRequestDetailsData?.reject_reason
                    ? t('DSR.TaskOverView.UpdateNote')
                    : t('DSR.TaskOverView.AddNote')}
                </AlertDialogTitle>
              </AlertDialogHeader>
              <AlertDialogDescription className="my-3 w-full">
                <Form {...form}>
                  <form className="space-y-4">
                    <FormField
                      control={form.control}
                      name="reject_reason"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea
                              placeholder={
                                approvedRequestDetailsData?.reject_reason
                                  ? 'Update rejection note...'
                                  : 'Enter rejection note...'
                              }
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              </AlertDialogDescription>
              <AlertDialogFooter className="flex justify-end">
                <Button
                  type="button"
                  className="mr-2 w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
                  onClick={handleRejectCancel}
                >
                  {t('Common.Cancel')}
                </Button>
                <Button
                  type="button"
                  className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
                  onClick={handleRejectSubmit}
                >
                  {t('DSR.TaskOverView.Reject')}
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </>
      )}
    </div>
  );
};

export default ApprovedRequestView;
