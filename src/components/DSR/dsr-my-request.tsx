import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { DSR_MY_REQUEST_DETAILS } from '../../../src/utils/routeConstant';
import { Button } from '../../@/components/ui/Common/Elements/Button/Button';
import { SkeletonCard } from '../../@/components/ui/Common/Elements/Skeleton/Skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../@/components/ui/Common/Table/Table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../@/components/ui/tooltip';
import { RootState } from '../../redux/store';
import { DataRequestItemPropperties } from '../../types/data-subject-rights';
import { encryptId } from '../../utils/cipher';
import { convertDateToHumanView } from '../common/CommonHelperFunctions';
import { fetchMyRequest } from '../common/services/data-subject-request';
import EmailVerificationModal from './common/email-verification-modal';
import OtpVerificationModal from './common/opt-verification-modal';
import GuestViewHeader from './Navbar/GuestViewHeader';

const DataRequestsPage = () => {
  const [data, setData] = useState<DataRequestItemPropperties[]>([]);
  const [isVerified, setIsVerified] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [showEmailModal, setShowEmailModal] = useState(true);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [verifiedEmail, setVerifiedEmail] = useState('');
  const token = useSelector((state: RootState) => state.dataSubjectRights.authToken);
  const getEmail = useSelector((state: RootState) => state.dataSubjectRights?.email);
  const getCustomerId = useSelector((state: RootState) => state.dataSubjectRights?.customer_id);
  const [dynamicData, setDynamicData] = useState<string>(''); // State for dynamic data only

  console.log(getCustomerId, 'getCustomerId');
  const [loading, setLoading] = useState(true);

  const columns: ColumnDef<DataRequestItemPropperties>[] = [
    {
      accessorKey: 'type',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Request Type
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <div>{row.getValue('type')}</div>,
    },
    {
      accessorKey: 'dsr_id',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          DSR ID
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <div>{row.getValue('dsr_id')}</div>,
    },
    {
      accessorKey: 'request_date',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Date
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div>
          {row.getValue('request_date')
            ? convertDateToHumanView(row.getValue<string>('request_date').toString())
            : '-'}
        </div>
      ),
    },
    {
      accessorKey: 'business_unit',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Entity
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <div>{row.getValue('business_unit')}</div>,
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Status
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.getValue('status');

        let buttonClass = 'border-2 px-4 py-2 rounded-md w-[200px] ';

        switch (status) {
          case 'APPROVED':
            buttonClass += 'border-[#2DB079] text-[#2DB079] bg-[#05CF781A] ';
            break;
          case 'COMPLETED':
            buttonClass += 'border-[#2DB079] text-[#2DB079] bg-[#05CF781A]';
            break;
          case 'REJECTED':
            buttonClass += 'border-[#ff0000] text-[#ff0000] bg-[#FFE5E5]';
            break;
          case 'PENDING':
            buttonClass += 'border-[#DE9609] text-[#DE9609] bg-[#FFC1461A]';
            break;
          case 'REJECTED_IN_PROGRESS':
            buttonClass += 'border-[#ff0000] text-[#ff0000] bg-[#FFE5E5]';
            break;
          default:
            buttonClass += 'border-[#ff0000] text-[#ff0000] bg-[#FFE5E5]';
        }

        const handleTooltipHover = async () => {
          switch (status) {
            case 'APPROVED':
              setDynamicData('The request has been approved.');
              break;
            case 'COMPLETED':
              setDynamicData('The Request has been completed successfully.');
              break;
            case 'REJECTED':
              setDynamicData('Request has been rejected.');
              break;
            case 'PENDING':
              setDynamicData('The request is pending and waiting for approval.');
              break;
            case 'REJECTED_IN_PROGRESS':
              setDynamicData('The request was rejected but is being processed.');
              break;
            default:
              setDynamicData('Status unknown. Please check with support.');
          }
          console.log(dynamicData, 'dynamicData');
        };

        return (
          <div className="flex items-center">
            <Button className={buttonClass}>
              {row.getValue('status') === 'REJECTED_IN_PROGRESS'
                ? (row.getValue('status') as string).replace(/_/g, ' ')
                : (row.getValue('status') as string)}
            </Button>

            {/* Tooltip display */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <svg
                    className="ml-2 hover:cursor-pointer"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    style={{ stroke: 'rgb(60 60 60 / var(--tw-text-opacity, 1))' }}
                    onMouseEnter={handleTooltipHover} // Fetch data on hover
                  >
                    <path
                      d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
                      stroke-width="1.5"
                    />
                    <path
                      d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M11.9922 8H12.0012"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </TooltipTrigger>
                <TooltipContent side="top" align="center">
                  {/* Display the dynamic data fetched on hover */}
                  <p className="text-white">{dynamicData}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        );
      },
    },
    {
      accessorKey: 'id',
      header: 'Action',
      cell: ({ row }) => {
        const navigate = useNavigate();
        const { type, dsr_id, business_unit, request_date, status, id } = row.original;
        const handleViewClick = () => {
          const encryptedId = encryptId(id);
          navigate(`${DSR_MY_REQUEST_DETAILS.slice(0, -1)}${encryptedId}`, {
            state: { type, dsr_id, business_unit, request_date, status, id, token },
          });
        };
        return (
          <Button
            className="h-[30px] w-[70px] p-0 text-custom-primary outline outline-1 outline-custom-primary"
            onClick={handleViewClick}
          >
            View
          </Button>
        );
      },
    },
  ];

  useEffect(() => {
    if (token || getEmail) {
      const loadMyRequests = async () => {
        try {
          const result = await fetchMyRequest(token);
          if (result?.data.success) {
            const formattedData = result?.data.result.rows.map((item: any) => {
              return {
                id: item.id,
                type: item.DsrRequestType.flowtype,
                business_unit: item.business_unit,
                request_date: item.createdAt,
                dsr_id: item.dsr_id,
                status: item.status,
              };
            });
            setData(formattedData);
          }
        } catch (error) {
          console.error('Error loading data:', error);
        } finally {
          setLoading(false);
        }
      };

      loadMyRequests();
    }
  }, [token]);

  const handleEmailVerified = (email: string) => {
    setVerifiedEmail(email);
    setShowEmailModal(false);
    setShowOtpModal(true);
  };

  const handleVerificationSuccess = () => {
    setIsVerified(true);
    setShowOtpModal(false);
  };
  const handleBackToEmail = () => {
    setShowOtpModal(false);
    setShowEmailModal(true);
    setVerifiedEmail('');
  };

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: { sorting },
  });

  const openInNewTab = (url: string) => {
    window.open(url, 'noopener,noreferrer');
  };
  if (!token) {
    return (
      <>
        <EmailVerificationModal
          isOpen={showEmailModal}
          onClose={() => setShowEmailModal(false)}
          onEmailVerified={handleEmailVerified}
        />
        <OtpVerificationModal
          isOpen={showOtpModal}
          onClose={() => setShowOtpModal(false)}
          onVerify={handleVerificationSuccess}
          email={verifiedEmail}
          onBack={handleBackToEmail}
        />
      </>
    );
  } else {
    return (
      token && (
        <div>
          <GuestViewHeader />
          <div className="mx-16 my-4 p-5">
            <div className="border-gray rounded-3xl border-2 pt-6">
              <div className="flex items-center justify-between border-b-2 border-gray-400 px-4 pb-4">
                <div>
                  <h1 className="text-2xl font-bold">My DSR Requests</h1>
                  <p className="text-sm text-gray-500">
                    View And Manage Your Data Subject Rights Requests
                  </p>
                </div>

                <div>
                  {/* <ImageButton
                    className="flex h-10 w-auto justify-end gap-2.5 rounded-lg bg-ucm-blue px-2.5 py-2.5"
                    onClick={() =>
                      getCustomerId
                        ? openInNewTab(
                            `${ASSIGNEE_REQUEST_FORM.slice(0, -1)}${encryptId(getCustomerId?.toString())}`
                          )
                        : ''
                    }
                  >
                    <img src={buttonPlusIcon} alt="create ticket icon"></img>
                    <p className="font-['Poppins'] text-xs font-medium text-white">
                      Create New Request
                    </p>
                  </ImageButton> */}
                </div>
              </div>

              {/* Table Component */}
              <div className="max-h-[750px] overflow-x-auto">
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <TableHead key={header.id}>
                            {header.isPlaceholder
                              ? null
                              : flexRender(header.column.columnDef.header, header.getContext())}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={columns.length} className="h-24 text-center">
                          <SkeletonCard />
                          <SkeletonCard />
                          <SkeletonCard />
                        </TableCell>
                      </TableRow>
                    ) : table.getRowModel().rows.length ? (
                      table.getRowModel().rows.map((row) => (
                        <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id}>
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={columns.length} className="h-24 text-center">
                          No result.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </div>
      )
    );
  }
};

export default DataRequestsPage;
