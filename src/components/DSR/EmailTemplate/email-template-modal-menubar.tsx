import { Bold, Italic, List, ListOrdered, Redo, UnderlineIcon, Undo } from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  EmailTemplateButtonWrapperProperties,
  EmailTemplateMenuBarProperties,
} from '../../../types/data-subject-rights';

const ButtonWrapper: React.FC<EmailTemplateButtonWrapperProperties> = ({
  onClick,
  isActive,
  children,
}) => (
  <Button
    onClick={onClick}
    className={`p-2 ${isActive ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
    variant="ghost"
  >
    {children}
  </Button>
);

const font = [
  { name: 'Default', value: 'Arial, sans-serif' },
  { name: 'Serif', value: 'Georgia, serif' },
  { name: 'Monospace', value: 'Courier, monospace' },
];

const colors = [
  { name: 'Black', value: '#000000' },
  { name: 'Red', value: '#FF0000' },
  { name: '<PERSON>', value: '#00FF00' },
  { name: 'Blue', value: '#0000FF' },
];

const EmailTemplateEditorMenuBar: React.FC<EmailTemplateMenuBarProperties> = ({
  editor,
  onContentChange,
}) => {
  const [_, setEditorState] = useState(0);

  const handleUpdate = useCallback(() => {
    if (editor) {
      const content = editor.getHTML();
      onContentChange(content);
      setEditorState((prev) => prev + 1);
    }
  }, [editor, onContentChange]);

  useEffect(() => {
    if (editor) {
      editor.on('update', handleUpdate);

      return () => {
        editor.off('update', handleUpdate);
      };
    }
  }, [editor, handleUpdate]);

  if (!editor) {
    return null;
  }

  return (
    <div className="flex items-center space-x-1 rounded p-1">
      <ButtonWrapper
        onClick={() => editor.chain().focus().toggleBold().run()}
        isActive={editor.isActive('bold')}
      >
        <Bold className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => editor.chain().focus().toggleItalic().run()}
        isActive={editor.isActive('italic')}
      >
        <Italic className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => editor.chain().focus().toggleUnderline().run()}
        isActive={editor.isActive('underline')}
      >
        <UnderlineIcon className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        isActive={editor.isActive('bulletList')}
      >
        <List className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => {
          console.log('OrderedList button clicked');
          editor.chain().focus().toggleOrderedList().run();
        }}
        isActive={editor.isActive('orderedList')}
      >
        <ListOrdered className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper onClick={() => editor.chain().focus().undo().run()} isActive={false}>
        <Undo className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper onClick={() => editor.chain().focus().redo().run()} isActive={false}>
        <Redo className="size-5" />
      </ButtonWrapper>
      {/* <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="p-2">
            <Type className="size-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {font.map((fontItem) => (
            <DropdownMenuItem
              key={fontItem.name}
              onClick={() => editor.chain().focus().setFontFamily(fontItem.value).run()}
            >
              {fontItem.name}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu> */}

      {/* <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="p-2">
            <Palette className="size-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {colors.map((color) => (
            <DropdownMenuItem
              key={color.name}
              onClick={() => editor.chain().focus().setColor(color.value).run()}
            >
              <div className="flex items-center">
                <div
                  className="mr-2 size-4 rounded-full"
                  style={{ backgroundColor: color.value }}
                />
                {color.name}
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu> */}
    </div>
  );
};

export default EmailTemplateEditorMenuBar;
