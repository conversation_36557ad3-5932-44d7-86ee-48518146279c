import { zodResolver } from '@hookform/resolvers/zod';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../@/components/ui/Common/Elements/Form/Form';
import { Input } from '../../../@/components/ui/Common/Elements/Input/Input';
import { Separator } from '../../../@/components/ui/Common/Elements/Seperator/Seperator';
import { CreateRequestFormData, CreateRequestFormProps } from '../../../types/data-subject-rights';
import EmailTemplateEditorMenuBar from './email-template-modal-menubar';

const formSchema = z.object({
  subject: z.string().min(1, 'Subject is required.'),
  editorContent: z.string().min(1, 'Email content is required.'),
});

const CreateRequestForm: React.FC<CreateRequestFormProps> = ({ onValidSubmit, initialData }) => {
  const form = useForm<CreateRequestFormData>({
    resolver: zodResolver(formSchema),
    mode: 'all',
    defaultValues: {
      subject: '',
      editorContent: '',
    },
  });
  const { t } = useTranslation();
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: false, // We're adding our own customized versions
        orderedList: false, // We're adding our own customized versions
      }),
      BulletList.configure({
        HTMLAttributes: {
          class: 'list-disc pl-6',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'list-decimal pl-6',
        },
      }),
      Underline,
      Placeholder.configure({
        placeholder: 'Email Content...',
      }),
    ],
  });
  const handleEditorUpdate = useCallback(
    (content: string) => {
      if (!content.trim()) {
        form.setError('editorContent', { message: 'Email content is required' });
      } else {
        form.clearErrors('editorContent');
      }
      form.setValue('editorContent', content, { shouldDirty: true, shouldValidate: true });
    },
    [form]
  );

  useEffect(() => {
    if (initialData) {
      form.reset({
        subject: form.getValues('subject') || initialData.subject || '',
        editorContent: initialData.editorContent || '',
      });

      if (initialData?.editorContent && editor) {
        // Only set content if it's different
        if (editor.getHTML() !== initialData.editorContent) {
          editor.commands.setContent(initialData.editorContent);
        }
      }
    }
  }, [initialData?.editorContent, editor]);

  useEffect(() => {
    const subscription = form.watch((value) => {
      if (value) {
        // Validate before submitting
        if (!value.subject?.trim() || !value.editorContent?.trim()) {
          return;
        }
        onValidSubmit({
          subject: value.subject,
          editorContent: value.editorContent,
        });
      }
    });

    return () => subscription.unsubscribe();
  }, [form, onValidSubmit]);

  const memoizedMenuBar = useMemo(
    () => <EmailTemplateEditorMenuBar editor={editor} onContentChange={handleEditorUpdate} />,
    [editor, handleEditorUpdate]
  );

  return (
    <div className="flex min-h-[500px] flex-col gap-2 p-4">
      <Form {...form}>
        <FormField
          control={form.control}
          name="subject"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t('DSR.EmailTemplate.TemplateName')}
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input placeholder="Subject" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </Form>
      <div className="rounded-md border">
        {memoizedMenuBar}
        <Separator className="bg-gray-200" />
        {editor && (
          <EditorContent
            editor={editor}
            className="p-2 [&_.ProseMirror]:min-h-[600px] [&_.ProseMirror]:!border-none [&_.ProseMirror]:!outline-none"
          />
        )}
      </div>
    </div>
  );
};

export default CreateRequestForm;
