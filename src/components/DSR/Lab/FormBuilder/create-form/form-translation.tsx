import { Trash2 } from 'lucide-react';
import React, { ChangeEvent, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/Button';
import { Combobox } from '../../../../../@/components/ui/Common/Elements/Combobox/Combobox';
import { Separator } from '../../../../../@/components/ui/Common/Elements/Seperator/Seperator';
import httpClient from '../../../../../api/httpClient';
import logo from '../../../../../assets/gotrustTitle_light.svg';
import solidEye from '../../../../../assets/IconoirEyeSolid.svg';
import { RootState } from '../../../../../redux/store';
import {
  CreateFieldControl,
  DSRFormBuilderControlRules,
  FormElement,
} from '../../../../../types/data-subject-rights';
import { encryptId } from '../../../../../utils/cipher';
import { DSR_FORM_BUILDER } from '../../../../../utils/routeConstant';
import { CREATE_FORM_TRANSLATION } from '../../../../common/api';
import {
  deleteDSRFormBuilderLanguage,
  fetchBasicInfoControl,
  fetchCustomEndpointData,
  fetchLanguageList,
  getDsrFormData,
  getTranslatedContent,
  getTranslatedLanguage,
} from '../../../../common/services/data-subject-request';
import { FormElementEditor } from './form-element-editor';
import { FormPreview } from './form-preview';
import { translateText } from './translationService';

interface FormTranslationProps {}

const FormTranslation: React.FC<FormTranslationProps> = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { formId } = location.state || {};

  const formBuilderData = useSelector(
    (state: RootState) => state.dataSubjectRights.formBuilderData
  );

  // Form builder state
  const [logoUrl, setLogoUrl] = useState<string>(logo);
  const [headerStyles, setHeaderStyles] = useState<{ backgroundColor: string; textColor: string }>({
    backgroundColor: '#000000',
    textColor: '#ffffff',
  });
  const [elements, setElements] = useState<FormElement[]>([]);
  const [selectedElement, setSelectedElement] = useState<FormElement | null>(null);
  const [activeView, setActiveView] = useState<'editor' | 'preview'>('editor');
  const [activeLeftTab, setActiveLeftTab] = useState<'basic' | 'elements'>('basic');
  const [inputData, setInputData] = useState(formBuilderData?.formName || '');
  const [formVersion, setFormVersion] = useState(formBuilderData?.actionVersion || '');
  const [isLoading, setIsLoading] = useState(true);
  const [layoutPreference, setLayoutPreference] = useState<1 | 2>(1);
  const [formRules, setFormRules] = useState<DSRFormBuilderControlRules[]>([]);
  const [headerDescription, setHeaderDescription] = useState<string>(
    'GoTrust Inc., including all its affiliates ("Company", "we," "our," and "us"), values the privacy rights of our customers, business partners, suppliers, vendors, users, and others. As required under applicable law, and specifically under the EU General Data Protection Regulation ("GDPR"), UK GDPR, and the California Consumer Privacy Act of 2018 ("CCPA") (collectively "Data Protection Laws"), individuals (including European Union and UK residents, and California residents, respectively) are permitted to make certain requests regarding our processing of their Personal Data.'
  );

  // Translation state
  const [selectedLanguage, setSelectedLanguage] = useState<string>('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [languages, setLanguages] = useState<Array<{ language_code: string; language: string }>>(
    []
  );
  const [isLoadingLanguages, setIsLoadingLanguages] = useState(false);
  const [contentData, setContentData] = useState<any>(null);
  const [translatedLanguages, setTranslatedLanguages] = useState<
    Array<{ id: number; language_code: string; language: string }>
  >([]);
  const [isLoadingTranslatedLanguages, setIsLoadingTranslatedLanguages] = useState(false);

  // View translation state
  const [viewingTranslation, setViewingTranslation] = useState(false);
  const [viewingLanguageCode, setViewingLanguageCode] = useState<string>('');
  const [translatedElements, setTranslatedElements] = useState<FormElement[]>([]);
  const [translatedHeaderDescription, setTranslatedHeaderDescription] = useState<string>('');
  const [translatedStaticTexts, setTranslatedStaticTexts] = useState<Record<string, string>>({});
  const [isLoadingTranslation, setIsLoadingTranslation] = useState(false);

  const getCustomerId = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );

  const chunkArray = (array: FormElement[], size: number): FormElement[][] => {
    const result = [];
    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size));
    }
    return result;
  };

  // Use translated elements when viewing translation, otherwise use original elements
  const displayElements = viewingTranslation ? translatedElements : elements;
  const displayHeaderDescription = viewingTranslation
    ? translatedHeaderDescription
    : headerDescription;
  const elementRows = chunkArray(displayElements, layoutPreference);

  // Transform languages for Combobox component
  const languageOptions = React.useMemo(() => {
    return languages.map((language) => ({
      value: language.language_code,
      label: language.language,
    }));
  }, [languages]);

  // Fetch translated languages list
  const fetchTranslatedLanguages = async () => {
    if (!formId || !getCustomerId) return;

    try {
      setIsLoadingTranslatedLanguages(true);

      // Encrypt customer ID and form ID before making the API call
      const encryptedCustomerId = encryptId(getCustomerId.toString());
      const encryptedFormId = encryptId(formId);

      const translatedData = await getTranslatedLanguage(encryptedFormId, encryptedCustomerId);

      // Set the translated languages from API response
      if (
        translatedData &&
        translatedData.success &&
        translatedData.result &&
        Array.isArray(translatedData.result.rows)
      ) {
        setTranslatedLanguages(translatedData.result.rows);
      }
    } catch (error) {
      console.error('Failed to fetch translated languages:', error);
      // Don't show error toast as this is not critical
    } finally {
      setIsLoadingTranslatedLanguages(false);
    }
  };

  // Fetch languages list
  useEffect(() => {
    const fetchLanguages = async () => {
      if (!getCustomerId) return;

      try {
        setIsLoadingLanguages(true);
        const languageData = await fetchLanguageList(getCustomerId);

        // Set the languages directly from API response
        if (languageData && Array.isArray(languageData)) {
          setLanguages(languageData);
        }
      } catch (error) {
        console.error('Failed to fetch languages:', error);
        toast.error('Failed to load languages. Please try again.');
      } finally {
        setIsLoadingLanguages(false);
      }
    };

    fetchLanguages();
  }, [getCustomerId]);

  // Fetch translated languages when formId and customerId are available
  useEffect(() => {
    fetchTranslatedLanguages();
  }, [formId, getCustomerId]);

  useEffect(() => {
    const fetchFormData = async () => {
      if (!formId || !getCustomerId) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // Encrypt customer ID and form ID before making the API call
        const encryptedCustomerId = encryptId(getCustomerId.toString());
        const encryptedFormId = encryptId(formId);

        // Fetch form content data to get styling information
        const formDataResponse = await getDsrFormData(encryptedCustomerId, encryptedFormId);

        if (formDataResponse.success && formDataResponse.result) {
          const formContent = formDataResponse.result.content;
          setContentData(formDataResponse.result);

          // Set form version from API response
          if (formDataResponse.result.actionVersion) {
            setFormVersion(formDataResponse.result.actionVersion);
          } else if (formDataResponse.result.action_version) {
            setFormVersion(formDataResponse.result.action_version);
          } else if (formDataResponse.result.version) {
            setFormVersion(formDataResponse.result.version);
          }

          if (formContent) {
            // Apply the actual form styling
            setLogoUrl(formContent.logoUrl || logo);
            setHeaderStyles({
              backgroundColor: formContent.headerBackgroundColor || '#000000',
              textColor: formContent.headerTextColor || '#ffffff',
            });
            setHeaderDescription(formContent.paragraphContent || headerDescription);
            setInputData(formContent.name || formBuilderData?.formName || '');
            setLayoutPreference(formContent.layoutPreference || 1);
          }
        }

        // Fetch form controls/questions
        const response = await fetchBasicInfoControl(formId);

        if (!response?.result?.rows) {
          console.error('Invalid response format:', response);
          return;
        }

        const controls: CreateFieldControl[] = response.result.rows;
        const validControls = controls.filter((control) => control !== null);

        const transformedElements: FormElement[] = [];
        for (const control of validControls) {
          try {
            const element = await transformControlToFormElement(control);
            transformedElements.push(element);
          } catch (error) {
            console.error('Failed to transform control:', control, error);
          }
        }

        setElements(transformedElements);

        const allRules = transformedElements.flatMap((el) => el.rules || []);
        setFormRules(allRules);
      } catch (error) {
        console.error('Failed to fetch form data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFormData();
  }, [formId, getCustomerId]);

  const transformControlToFormElement = async (
    control: CreateFieldControl
  ): Promise<FormElement> => {
    const baseElement: FormElement = {
      id: control.id.toString(),
      order: control.order,
      is_optional: control.is_optional,
      type: control.artifact_type as FormElement['type'],
      label: control.title,
      placeholder: control.placeholder || null,
      required: !control.is_optional,
      question: control.question || '',
      helpText: '',
      question_id: control.question_id,
      options:
        control.fields?.map((field) => ({
          id: field.id.toString(),
          label: field.name,
          value: field.name,
        })) || [],
      is_disable: control?.is_disabled,
      rules: control.rules || [],
      rule_applied: control.rule_applied === '1',
      endpoint: control.endpoint || '',
      is_custom: control.is_custom || false, // Added is_custom property
    };

    if (control.artifact_type === 'custom_select' && control.endpoint && getCustomerId) {
      try {
        const endpointResponse = await fetchCustomEndpointData(
          control.endpoint,
          getCustomerId,
          (formBuilderData as any)?.busi_unit_id
        );

        if (endpointResponse.success && endpointResponse.result) {
          const endpointData = Array.isArray(endpointResponse.result)
            ? endpointResponse.result
            : [];

          if (control.endpoint === '/guest-workflow/published') {
            baseElement.options = endpointData.map((data: any) => ({
              id: data?.id?.toString(),
              label: data?.flowtype?.replace(/_/g, ' '),
              value: data?.id,
            }));
          } else if (control.endpoint === '/country') {
            baseElement.options = endpointData.map((data: any) => ({
              id: data?.id?.toString(),
              label: data?.country_name?.replace(/_/g, ' '),
              value: data?.id?.toString(), // Ensure value is always string for consistency
            }));
          } else {
            baseElement.options = endpointData.map((data: any) => ({
              id: data?.id?.toString(),
              label: data?.name || data?.label || data?.value,
              value: data?.value || data?.id,
            }));
          }
        }
      } catch (err) {
        console.error(`Error fetching endpoint data for ${control.endpoint}:`, err);
      }
    }

    return baseElement;
  };

  const handleInput = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setInputData(value);
  };

  const selectElement = (element: FormElement) => {
    setSelectedElement(element);
  };

  const deleteElement = async () => {
    // For translation view, we don't allow deletion
    toast.error('Elements cannot be deleted in translation view');
  };

  const handleTranslate = async () => {
    if (!selectedLanguage || !formId || !getCustomerId) {
      toast.error('Form ID, Customer ID, and language selection are required for translation');
      return;
    }

    setIsTranslating(true);
    try {
      const textsToTranslate: string[] = [];
      const textKeys: string[] = [];

      // Add static texts to translation
      const staticTexts = [
        'Data Subject Request Form',
        'Submit Request',
        'Submitting...',
        'Powered by',
        'Select an option',
        'Pick a date',
        'Upload file',
        'Change file',
        'Selected file',
        'Please enter a valid email address',
        'Enter',
        '(Max file: 5 and Max size: 100MB)',
      ];

      const staticTextKeys = [
        'formTitle',
        'submitButton',
        'submittingText',
        'poweredBy',
        'selectOptionDefault',
        'pickDateDefault',
        'uploadFileText',
        'changeFileText',
        'selectedFileText',
        'emailValidationError',
        'enterPlaceholder',
        'fileSizeLimitText',
      ];

      // Add static texts to translation arrays
      staticTexts.forEach((text, index) => {
        textsToTranslate.push(text);
        textKeys.push(staticTextKeys[index]);
      });

      // Add header description if available
      if (contentData?.content?.paragraphContent) {
        textsToTranslate.push(contentData.content.paragraphContent);
        textKeys.push('paragraphContent');
      }

      // Collect all form element texts that need translation
      for (const element of elements) {
        textsToTranslate.push(element.label);
        textKeys.push(`label_${element.id}`);

        // Handle placeholder text - include default placeholders for empty ones
        let placeholderText = element.placeholder;
        if (!placeholderText) {
          // Set default placeholder based on element type
          if (
            element.type === 'input' ||
            element.type === 'number' ||
            element.type === 'textarea'
          ) {
            placeholderText = 'Enter';
          } else if (element.type === 'select' || element.type === 'custom_select') {
            placeholderText = 'Select an option';
          }
        }

        if (placeholderText) {
          textsToTranslate.push(placeholderText);
          textKeys.push(`placeholder_${element.id}`);
        }

        if (element.helpText) {
          textsToTranslate.push(element.helpText);
          textKeys.push(`helpText_${element.id}`);
        }

        // Add options for select, radio, and checkbox elements
        if (element.options) {
          element.options.forEach((option, index) => {
            textsToTranslate.push(option.label);
            textKeys.push(`option_${element.id}_${index}`);
          });
        }
      }

      // Split into batches of 120 (staying safely under the 128 limit)
      const batchSize = 120;
      const newTranslatedTexts: Record<string, string> = {};

      // Process in batches
      for (let i = 0; i < textsToTranslate.length; i += batchSize) {
        const batchTexts = textsToTranslate.slice(i, i + batchSize);
        const batchKeys = textKeys.slice(i, i + batchSize);

        // Translate the current batch
        const translatedBatch = await translateText(batchTexts, selectedLanguage);

        // Store the translated results
        translatedBatch.forEach((translatedText: string, index: number) => {
          newTranslatedTexts[batchKeys[index]] = translatedText;
        });

        // Add a small delay between batches to avoid rate limiting
        if (i + batchSize < textsToTranslate.length) {
          await new Promise((resolve) => setTimeout(resolve, 300));
        }
      }

      // Save translations to backend
      await saveFormTranslation(selectedLanguage, newTranslatedTexts);

      const selectedLanguageName = languages.find(
        (l) => l.language_code === selectedLanguage
      )?.language;
      toast.success(`Form successfully translated to ${selectedLanguageName}`);

      // Refresh the translated languages list
      await fetchTranslatedLanguages();

      // navigate(DSR_FORM_BUILDER);
    } catch (error) {
      console.error('Translation error:', error);
      toast.error('Failed to translate form content');
    } finally {
      setIsTranslating(false);
    }
  };

  const saveFormTranslation = async (
    languageCode: string,
    translations: Record<string, string>
  ) => {
    try {
      // Use the form ID from props
      if (!formId || !getCustomerId) {
        console.warn('No form ID or customer ID available for saving translation');
        return;
      }

      // Fetch the complete form data structure
      const encryptedCustomerId = encryptId(getCustomerId.toString());
      const encryptedFormId = encryptId(formId);
      const formDataResponse = await getDsrFormData(encryptedCustomerId, encryptedFormId);

      if (!formDataResponse.success || !formDataResponse.result) {
        console.error('Failed to fetch form data for translation');
        return;
      }

      // Get the complete form structure
      const completeFormData = formDataResponse.result;

      // Create translated content object mapping question IDs to translated text
      const translatedContent: Record<string, string> = {};

      for (const element of elements) {
        const labelKey = `label_${element.id}`;
        if (translations[labelKey]) {
          translatedContent[element.id] = translations[labelKey];
        }

        // Add placeholder translations if available (including default placeholders)
        const placeholderKey = `placeholder_${element.id}`;
        if (translations[placeholderKey]) {
          translatedContent[`${element.id}_placeholder`] = translations[placeholderKey];
        }

        // Add option translations if available
        if (element.options) {
          element.options.forEach((_, index) => {
            const optionKey = `option_${element.id}_${index}`;
            if (translations[optionKey]) {
              translatedContent[`${element.id}_option_${index}`] = translations[optionKey];
            }
          });
        }
      }

      // Create the payload for translation API
      const payload = {
        form_id: formId,
        language_code: languageCode,
        language: languages.find((l) => l.language_code === languageCode)?.language || languageCode,
        content: {
          // Include the complete form structure
          ...completeFormData,
          // Keep content object as is (same as English)
          content: completeFormData.content,
          // Add translated paragraphContent if available
          paragraphContent:
            translations['paragraphContent'] || completeFormData.content?.paragraphContent,
          // Add static text translations
          formTitle: translations['formTitle'],
          submitButton: translations['submitButton'],
          submittingText: translations['submittingText'],
          poweredBy: translations['poweredBy'],
          selectOptionDefault: translations['selectOptionDefault'],
          pickDateDefault: translations['pickDateDefault'],
          uploadFileText: translations['uploadFileText'],
          changeFileText: translations['changeFileText'],
          selectedFileText: translations['selectedFileText'],
          emailValidationError: translations['emailValidationError'],
          enterPlaceholder: translations['enterPlaceholder'],
          fileSizeLimitText: translations['fileSizeLimitText'],
          // Include DSRCustomerControls with translated fields
          DSRCustomerControls:
            completeFormData.DSRCustomerControls?.map((control: any) => ({
              ...control,
              // Apply translations to control fields if available
              title: translatedContent[control.id] || control.title,
              placeholder: translatedContent[`${control.id}_placeholder`] || control.placeholder,
              fields:
                control.fields?.map((field: any, index: number) => ({
                  ...field,
                  name: translatedContent[`${control.id}_option_${index}`] || field.name,
                })) || control.fields,
            })) || [],
        },
      };

      await httpClient.post(CREATE_FORM_TRANSLATION, payload);
    } catch (error) {
      console.error('Failed to save form translation:', error);
      throw error;
    }
  };

  const handleDeleteTranslation = async (translationId: number) => {
    try {
      await deleteDSRFormBuilderLanguage(translationId);
      toast.success('Translation deleted successfully');
      // Refresh the translated languages list
      await fetchTranslatedLanguages();
    } catch (error) {
      console.error('Failed to delete translation:', error);
      toast.error('Failed to delete translation');
    }
  };

  const handleViewTranslation = async (languageCode: string) => {
    if (!formId || !getCustomerId) {
      toast.error('Form ID and Customer ID are required to view translation');
      return;
    }

    try {
      setIsLoadingTranslation(true);
      setViewingTranslation(true);
      setViewingLanguageCode(languageCode);

      // Encrypt customer ID and form ID before making the API call
      const encryptedCustomerId = encryptId(getCustomerId.toString());
      const encryptedFormId = encryptId(formId);

      if (languageCode === 'en') {
        // For English, show the original content
        setTranslatedElements(elements);
        setTranslatedHeaderDescription(headerDescription);
        setTranslatedStaticTexts({});
      } else {
        // For other languages, fetch translated content
        const translatedContentResponse = await getTranslatedContent(
          encryptedFormId,
          encryptedCustomerId,
          languageCode
        );

        if (translatedContentResponse?.success && translatedContentResponse?.result) {
          const translatedData = translatedContentResponse.result;
          // Create a mapping of translated texts from DSRCustomerControls
          const translatedTexts: Record<string, string> = {};
          const staticTexts: Record<string, string> = {};

          // Handle static form text translations first
          const staticTextMappings = {
            formTitle: 'Data Subject Request Form',
            submitButton: 'Submit Request',
            submittingText: 'Submitting...',
            poweredBy: 'Powered by',
            selectOptionDefault: 'Select an option',
            pickDateDefault: 'Pick a date',
            uploadFileText: 'Upload File',
            changeFileText: 'Change File',
            selectedFileText: 'Selected file:',
            emailValidationError: 'Please enter a valid email address',
            enterPlaceholder: 'Enter',
            fileSizeLimitText: '(Max file: 5 and Max size: 100MB)',
          };

          Object.keys(staticTextMappings).forEach((key) => {
            if (translatedData.content && translatedData.content[key]) {
              staticTexts[key] = translatedData.content[key];
            }
          });

          // Handle form content translations (title, paragraph, etc.)
          if (translatedData.content && translatedData.content.content) {
            Object.keys(translatedData.content.content).forEach((key) => {
              staticTexts[key] = translatedData.content.content[key];
            });
          }

          // Handle DSRCustomerControls translations (this is the main source)
          if (translatedData.content && translatedData.content.DSRCustomerControls) {
            translatedData.content.DSRCustomerControls.forEach((control: any) => {
              // Map control title
              if (control.title) {
                translatedTexts[`label_${control.id}`] = control.title;
              }

              // Map control placeholder
              if (control.placeholder) {
                translatedTexts[`placeholder_${control.id}`] = control.placeholder;
              }

              // Map control field options
              if (control.fields) {
                control.fields.forEach((field: any, index: number) => {
                  if (field.name) {
                    translatedTexts[`option_${control.id}_${index}`] = field.name;
                  }
                });
              }
            });
          }

          // Handle direct content mapping (for backward compatibility)
          if (translatedData.content) {
            Object.keys(translatedData.content).forEach((questionId) => {
              if (
                questionId !== 'content' &&
                questionId !== 'DSRCustomerControls' &&
                questionId !== 'paragraphContent'
              ) {
                const translatedText = translatedData.content[questionId];
                if (typeof translatedText === 'string') {
                  translatedTexts[`label_${questionId}`] = translatedText;
                }
              }
            });
          }

          // Fetch country data once if needed for any country elements
          let countryData: any[] = [];
          const hasCountryElements = elements.some(
            (element) =>
              element.endpoint === '/country' && element.options && element.options.length > 0
          );

          if (hasCountryElements && languageCode !== 'en') {
            try {
              const endpointResponse = await fetchCustomEndpointData(
                '/country',
                getCustomerId,
                '1'
              );
              if (endpointResponse.success && endpointResponse.result) {
                countryData = endpointResponse.result;
              } else {
                console.warn('Failed to fetch country data:', endpointResponse);
              }
            } catch (error) {
              console.error('Error fetching country data for translation:', error);
            }
          }

          // Apply translations to elements using the mapped texts
          const translatedElementsCopy = await Promise.all(
            elements.map(async (element) => {
              const translatedElement = { ...element };

              // Apply label translation
              const labelKey = `label_${element.id}`;
              if (translatedTexts[labelKey]) {
                translatedElement.label = translatedTexts[labelKey];
              }

              // Apply placeholder translation (including default placeholders)
              const placeholderKey = `placeholder_${element.id}`;
              if (translatedTexts[placeholderKey]) {
                translatedElement.placeholder = translatedTexts[placeholderKey];
              } else if (!element.placeholder) {
                // For elements that originally had empty placeholders, check if we have a translated default
                if (
                  element.type === 'input' ||
                  element.type === 'number' ||
                  element.type === 'textarea'
                ) {
                  translatedElement.placeholder = translatedTexts['enterPlaceholder'] || 'Enter';
                } else if (element.type === 'select' || element.type === 'custom_select') {
                  translatedElement.placeholder =
                    translatedTexts['selectOptionDefault'] || 'Select an option';
                }
              }

              // Apply help text translation (using placeholder as fallback)
              const helpTextKey = `helpText_${element.id}`;
              if (translatedTexts[helpTextKey]) {
                translatedElement.helpText = translatedTexts[helpTextKey];
              } else if (translatedTexts[placeholderKey]) {
                translatedElement.helpText = translatedTexts[placeholderKey];
              }

              // Apply options translation
              if (element.options && element.options.length > 0) {
                // Do NOT translate options if they come from an endpoint
                if (element.endpoint) {
                  // Use options as received from endpoint, do not translate
                  translatedElement.options = element.options;
                } else {
                  // Translate only static options
                  translatedElement.options = element.options.map((option, index) => {
                    const optionKey = `option_${element.id}_${index}`;
                    return {
                      ...option,
                      label: translatedTexts[optionKey] || option.label,
                    };
                  });
                }
              }

              return translatedElement;
            })
          );

          setTranslatedElements(translatedElementsCopy);
          setTranslatedStaticTexts(staticTexts);

          // Apply header description translation
          if (translatedData.content && translatedData.content.paragraphContent) {
            setTranslatedHeaderDescription(translatedData.content.paragraphContent);
          } else if (staticTexts['paragraphContent']) {
            setTranslatedHeaderDescription(staticTexts['paragraphContent']);
          } else {
            setTranslatedHeaderDescription(headerDescription);
          }
        } else {
          toast.error('Failed to load translated content');
          setViewingTranslation(false);
        }
      }
    } catch (error) {
      console.error('Failed to view translation:', error);
      toast.error('Failed to load translation view');
      setViewingTranslation(false);
    } finally {
      setIsLoadingTranslation(false);
    }
  };

  const handleCancel = () => {
    navigate(DSR_FORM_BUILDER);
  };

  return (
    <div className="flex max-h-[80vh] w-full flex-col overflow-auto p-4">
      <div className="flex flex-1 gap-4 overflow-hidden bg-gray-50">
        {/* Left Sidebar - 3/4 of the layout showing the form builder */}
        <div className="flex w-3/4 gap-4">
          <div className="flex min-w-[50%] flex-1 flex-col overflow-auto rounded-md bg-white">
            <div className="h-12 p-1">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 p-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">Form Name:</span>
                    <h1 className="text-sm font-medium text-gray-900">{inputData}</h1>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">Version:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {formVersion || 'N/A'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <Separator className="bg-gray-300" />

            <div className="flex-1 overflow-auto p-3">
              <div
                className="mb-2 flex items-center justify-between rounded-t-md p-4 text-white"
                style={{
                  backgroundColor: headerStyles?.backgroundColor,
                  color: headerStyles?.textColor,
                }}
              >
                <div className="flex items-center gap-2">
                  <h2 className="text-lg font-semibold">
                    {viewingTranslation && translatedStaticTexts?.formTitle
                      ? translatedStaticTexts.formTitle
                      : 'Data Subject Request Form'}
                  </h2>
                </div>
                <div className="flex items-center gap-2">
                  <img src={logoUrl} alt="Logo" className="h-12 w-20 object-contain" />
                </div>
              </div>
              <div className="px-4 py-2 text-sm text-gray-600">{displayHeaderDescription}</div>
              {isLoading || isLoadingTranslation ? (
                <div className="mt-5 flex h-[80vh] items-start justify-center">
                  <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
                  {isLoadingTranslation && (
                    <span className="ml-2 text-sm text-gray-500">Loading translation...</span>
                  )}
                </div>
              ) : activeView === 'editor' ? (
                <div className="mx-auto">
                  {elementRows.map((row, rowIndex) => (
                    <div
                      key={rowIndex}
                      className={`mb-4 flex gap-4 ${
                        layoutPreference === 1 ? 'flex-col' : 'flex-row'
                      }`}
                    >
                      {row.map((element) => (
                        <div
                          key={element.id}
                          className={`flex-1 ${layoutPreference === 1 ? 'w-full' : 'w-1/2'}`}
                        >
                          <FormElementEditor
                            element={element}
                            onDelete={deleteElement}
                            onSelect={selectElement}
                            isSelected={selectedElement?.id === element.id}
                            translatedTexts={viewingTranslation ? translatedStaticTexts : undefined}
                          />
                        </div>
                      ))}
                    </div>
                  ))}
                  {/* Submit Request Button */}
                  {displayElements.length > 0 && (
                    <div className="flex justify-end pt-6">
                      <Button
                        type="button"
                        className="bg-custom-primary text-white hover:bg-custom-primary"
                        disabled
                      >
                        {viewingTranslation && translatedStaticTexts?.submitButton
                          ? translatedStaticTexts.submitButton
                          : 'Submit Request'}
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="mx-auto">
                  <FormPreview
                    elements={displayElements}
                    translatedTexts={viewingTranslation ? translatedStaticTexts : undefined}
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Sidebar - 1/4 of the layout for translation functionality */}
        <div className="w-1/4 rounded-md bg-white p-4">
          <div className="mb-3">
            <h2 className="text-xl font-bold text-gray-900">Translate Form </h2>
          </div>
          <Separator className="bg-gray-300" />

          <div className="mt-1 space-y-6">
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                {t('DSR.FormBuilder.SelectLanguage')}
              </label>
              <Combobox
                options={languageOptions}
                value={selectedLanguage}
                onChange={setSelectedLanguage}
                placeholder={
                  isLoadingLanguages ? 'Loading languages...' : t('DSR.FormBuilder.ChooseLanguage')
                }
                className="w-full"
                disabled={isLoadingLanguages}
              />
            </div>

            {/* Translated Languages List */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Translated Languages
              </label>
              {isLoadingTranslatedLanguages ? (
                <div className="flex items-center justify-center py-4">
                  <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-primary"></div>
                  <span className="ml-2 text-sm text-gray-500">Loading...</span>
                </div>
              ) : (
                <div className="max-h-64 space-y-2 overflow-y-auto pr-2 scrollbar-hide">
                  {/* English as default language */}
                  <div
                    className={`flex items-center justify-between rounded-md border border-gray-200 px-3 py-2 ${
                      viewingLanguageCode === 'en' ? 'border-blue-300 bg-blue-100' : 'bg-white'
                    }`}
                  >
                    <span className="text-sm font-medium text-gray-700">English</span>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewTranslation('en')}
                        className="text-blue-600 hover:text-blue-800"
                        title="View"
                      >
                        {/* <Eye className="h-4 w-4" /> */}
                        <img className="size-5" src={solidEye} alt="eye icon" />
                      </button>
                    </div>
                  </div>

                  {/* Translated languages */}
                  {translatedLanguages.map((lang) => (
                    <div
                      key={lang.language_code}
                      className={`flex items-center justify-between rounded-md border border-gray-200 px-3 py-2 ${
                        viewingLanguageCode === lang.language_code
                          ? 'border-blue-300 bg-blue-100'
                          : 'bg-gray-50'
                      }`}
                    >
                      <span className="text-sm font-medium text-gray-700">{lang.language}</span>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleViewTranslation(lang.language_code)}
                          className="text-blue-600 hover:text-blue-800"
                          title="View"
                        >
                          {/* <Eye className="h-4 w-4" /> */}
                          <img className="size-5" src={solidEye} alt="eye icon" />
                        </button>
                        <button
                          onClick={() => handleDeleteTranslation(lang.id)}
                          className="text-red-600 hover:text-red-800"
                          title="Delete"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}

                  {translatedLanguages.length === 0 && (
                    <div className="rounded-md border border-gray-200 bg-gray-50 px-3 py-4 text-center">
                      <span className="text-sm text-gray-500">
                        No additional translations available yet
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* {publicUrl && (
              <div className="rounded-md bg-gray-50 p-4">
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  {t('DSR.FormBuilder.FormURL')}
                </label>
                <div className="break-all text-sm text-gray-600">{publicUrl}</div>
              </div>
            )} */}
          </div>
        </div>
      </div>

      <div className="sticky bottom-0 mt-3 flex justify-end gap-3 rounded-md bg-gray-100 pt-2">
        <Button variant="outline" onClick={handleCancel} disabled={isTranslating}>
          {t('DSR.FormBuilder.Cancel')}
        </Button>
        <Button
          className="bg-custom-primary text-white hover:bg-custom-primary"
          onClick={handleTranslate}
          disabled={!selectedLanguage || isTranslating}
        >
          {isTranslating ? t('DSR.FormBuilder.Translating') : t('DSR.FormBuilder.StartTranslation')}
        </Button>
      </div>
    </div>
  );
};

export default FormTranslation;
