import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  rectSortingStrategy,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Columns2, Edit, Eye, Plus, Square } from 'lucide-react';
import React, { ChangeEvent, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Dialog,
  DialogContent,
} from '../../../../../@/components/ui/Common/Elements/Dialog/Dialog';
import { Input } from '../../../../../@/components/ui/Common/Elements/Input/Input';
import { Separator } from '../../../../../@/components/ui/Common/Elements/Seperator/Seperator';
import logo from '../../../../../assets/gotrustTitle_light.svg';
import {
  setFormBuilderData,
  setUpdatedNameFormBuilderTableData,
} from '../../../../../redux/reducers/DSR/dsr-slice';
import { RootState } from '../../../../../redux/store';
import {
  CreateFieldControl,
  CreateFormPayload,
  DSRFormBuilderControlRules,
  FormElement,
} from '../../../../../types/data-subject-rights';
import { encryptId } from '../../../../../utils/cipher';
import {
  DSR_FORM_BUILDER,
  DSR_FORM_TRANSLATION,
  GUEST_REQUEST_FORM,
} from '../../../../../utils/routeConstant';
import {
  createFormVersion,
  createQuestion,
  deleteDSRFormBuilderQuestion,
  fetchBasicInfoControl,
  fetchCustomEndpointData,
  publishDsrForm,
  submitDSRFormContent,
  updateDSRControls,
  updateQuestionOrder,
} from '../../../../common/services/data-subject-request';
import LeftNewFormBuilder from './create-form-leftside';
import RightNewFormBuilder from './create-form-rightside';
import { FormElementEditor } from './form-element-editor';
import { FormPreview } from './form-preview';

// Sortable Item Component for dnd-kit
interface SortableItemProps {
  id: string;
  element: FormElement;
  onDelete: (id: string) => void;
  onSelect: (element: FormElement) => void;
  isSelected: boolean;
}

function SortableItem({ id, element, onDelete, onSelect, isSelected }: SortableItemProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 'auto',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={`${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
    >
      <div {...listeners} className="w-full">
        <FormElementEditor
          element={element}
          onDelete={onDelete}
          onSelect={onSelect}
          isSelected={isSelected}
        />
      </div>
    </div>
  );
}

function CreateForm() {
  const location = useLocation();
  const dispatch = useDispatch();
  const formBuilderData = useSelector(
    (state: RootState) => state.dataSubjectRights.formBuilderData
  );

  // console.log(formBuilderData, 'formBuilderData12');
  const { formData, contentData } = location.state || {};
  const [logoUrl, setLogoUrl] = useState<string>(contentData?.logoUrl || logo);
  const [logoHeight, setLogoHeight] = useState<number>(60); // Default height, adjust as needed
  const [logoWidth, setLogoWidth] = useState<number>(120); // Default width, adjust as needed
  const [headerStyles, setHeaderStyles] = useState<{ backgroundColor: string; textColor: string }>({
    backgroundColor: '#000000',
    textColor: '#ffffff',
  });

  const [modifiedQuestions, setModifiedQuestions] = useState<Set<string>>(new Set());

  const [masterElements, setMasterElements] = useState<FormElement[]>([]);
  const [saveModalOpen, setSaveModalOpen] = useState(false);
  const [translationModalOpen, setTranslationModalOpen] = useState(false);

  const [customElements, setCustomElements] = useState<FormElement[]>([]);
  const [elements, setElements] = useState<FormElement[]>([]);
  const [selectedElement, setSelectedElement] = useState<FormElement | null>(null);
  const [activeView, setActiveView] = useState<'editor' | 'preview'>('editor');
  const [activeLeftTab, setActiveLeftTab] = useState<'basic' | 'elements'>('basic');
  const [inputData, setInputData] = useState(formBuilderData.formName);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const navigate = useNavigate();
  const [layoutPreference, setLayoutPreference] = useState<1 | 2>(
    contentData?.layoutPreference ? contentData?.layoutPreference : 1
  );
  const [showUploadDocuments, setShowUploadDocuments] = useState(false);
  const { t } = useTranslation();

  const [publicUrl, setPublicUrl] = useState<string>('');

  const [headerDescription, setHeaderDescription] = useState<string>(
    'GoTrust Inc., including all its affiliates ("Company", "we," "our," and "us"), values the privacy rights of our customers, business partners, suppliers, vendors, users, and others. As required under applicable law, and specifically under the EU General Data Protection Regulation ("GDPR"), UK GDPR, and the California Consumer Privacy Act of 2018 ("CCPA") (collectively "Data Protection Laws"), individuals (including European Union and UK residents, and California residents, respectively) are permitted to make certain requests regarding our processing of their Personal Data.'
  );

  const getCustomerId = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );

  const [formRules, setFormRules] = useState<DSRFormBuilderControlRules[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);

  const handleFormRulesChange = (newFormRules: DSRFormBuilderControlRules) => {
    if (newFormRules) {
      setFormRules([...formRules, newFormRules]);
    }
  };

  const handleLogoChange = (newLogoUrl: string) => {
    setLogoUrl(newLogoUrl);
  };

  const handleHeaderStyleChange = (styles: { backgroundColor: string; textColor: string }) => {
    setHeaderStyles(styles);
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const urlFromQuery = searchParams.get('url');

    if (urlFromQuery && !publicUrl) {
      const currentLanguage = localStorage.getItem('language') || 'en';
      let finalUrl = decodeURIComponent(urlFromQuery);
      try {
        if (finalUrl.includes('?')) {
          const [baseUrl, queryString] = finalUrl.split('?');
          const params = new URLSearchParams(queryString);
          params.set('language_code', currentLanguage);

          finalUrl = `${baseUrl}?${params.toString()}`;
        } else {
          finalUrl = `${finalUrl}?language_code=${currentLanguage}`;
        }
      } catch (error) {
        console.error('Error processing URL from query params:', error);
        finalUrl = `${finalUrl}?language_code=${currentLanguage}`;
      }

      setPublicUrl(finalUrl);
    }
  }, [publicUrl]);

  useEffect(() => {
    if (elements.some((el) => el.id.startsWith('temp-'))) {
      setHasUnsavedChanges(true);
    } else {
      setHasUnsavedChanges(false);
    }
  }, [elements]);

  useEffect(() => {
    if (selectedElement?.label === 'Are you a data subject?') {
      const selectedValue = selectedElement.options?.find((option) => option.selected)?.value;
      setShowUploadDocuments(selectedValue === 'no');
    }
  }, [selectedElement]);

  useEffect(() => {
    const fetchInitialQuestions = async () => {
      if (!formBuilderData?.id) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetchBasicInfoControl(formBuilderData.id);

        if (!response?.result?.rows) {
          console.error('Invalid response format:', response);
          return;
        }

        const controls: CreateFieldControl[] = response.result.rows;

        const validControls = controls.filter((control) => control !== null);

        const transformedElements: FormElement[] = [];
        for (const control of validControls) {
          try {
            const element = await transformControlToFormElement(control);
            transformedElements.push(element);
          } catch (error) {
            console.error('Failed to transform control:', control, error);
          }
        }

        setMasterElements(transformedElements);
        setElements(transformedElements);

        const allRules = transformedElements.flatMap((el) => el.rules || []);
        setFormRules(allRules);
      } catch (error) {
        console.error('Failed to fetch initial questions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialQuestions();
  }, [formBuilderData?.id]);

  const transformControlToFormElement = async (
    control: CreateFieldControl
  ): Promise<FormElement> => {
    const baseElement: FormElement = {
      id: control.id.toString(),
      order: control.order,
      is_optional: control.is_optional,
      type: control.artifact_type as FormElement['type'],
      label: control.title,
      placeholder: control.placeholder || null,
      required: !control.is_optional,
      question: control.question || '',
      helpText: '',
      question_id: control.question_id,
      options:
        control.fields?.map((field) => ({
          id: field.id.toString(),
          label: field.name,
          value: field.name,
        })) || [],
      is_custom: control.is_custom,
      is_disable: control?.is_disabled,
      rules: control.rules || [],
      rule_applied: control.rule_applied === '1',
      endpoint: control.endpoint || '',
    };

    if (control.artifact_type === 'custom_select' && control.endpoint && getCustomerId) {
      try {
        const endpointResponse = await fetchCustomEndpointData(
          control.endpoint,
          getCustomerId,
          formData.Group.id
        );

        if (endpointResponse.success && endpointResponse.result) {
          const endpointData = Array.isArray(endpointResponse.result)
            ? endpointResponse.result
            : [];

          if (control.endpoint === '/guest-workflow/published') {
            baseElement.options = endpointData.map((data: any) => ({
              id: data?.id?.toString(),
              label: data?.flowtype?.replace(/_/g, ' '),
              value: data?.id,
            }));
          } else if (control.endpoint === '/country') {
            baseElement.options = endpointData.map((data: any) => ({
              id: data?.id?.toString(),
              label: data?.country_name?.replace(/_/g, ' '),
              value: data?.id?.toString(), // Ensure value is always string for consistency
            }));
          } else {
            baseElement.options = endpointData.map((data: any) => ({
              id: data?.id?.toString(),
              label: data?.name || data?.label || data?.value,
              value: data?.value || data?.id,
            }));
          }
        }
      } catch (err) {
        console.error(`Error fetching endpoint data for ${control.endpoint}:`, err);
      }
    }

    return baseElement;
  };

  const transformFormElementToControl = (
    element: FormElement,
    isUpdate: boolean = false,
    formId?: string
  ): CreateFormPayload => {
    const fields =
      element.options?.map((option, index) => ({
        id: parseInt(option.id) || index,
        name: option.label || `Option ${index + 1}`,
        selected: false,
      })) || [];

    const elementRules = formRules.filter((rule) => rule.questionID === element.id);

    const basePayload: any = {
      title: element.label || `${element.order}`,
      placeholder: element.placeholder || null,
      artifact_type: element.type,
      is_attachment: element.type === 'file',
      question: element.question || `${element.label || 'this topic'}`,
      fields: fields,
      rule_applied: elementRules.length > 0 ? '1' : '0',
      is_optional: !element.required,
    };
    console.log('Transforming element to control:', {
      id: element.id,
      placeholder: element.placeholder,
      description: basePayload.description,
    });

    if (elementRules.length > 0) {
      basePayload.rules = elementRules;
    }

    if (isUpdate) {
      return basePayload;
    }

    return {
      ...basePayload,
      order: element.order,
      form_id: formId || formBuilderData?.id,
      customer_id: getCustomerId,
      extra_input: false,
      extra_input_type: 'input',
    };
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const type = e.dataTransfer.getData('elementType') as FormElement['type'];

    if (!type) {
      console.error('No elementType found in dataTransfer');
      return;
    }

    const tempId = `temp-${crypto.randomUUID()}`;

    const newElement: FormElement = {
      id: tempId,
      order: elements.length + 1,
      is_optional: true,
      type,
      label: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
      placeholder: null,
      required: false,
      question: '',
      helpText: '',
      question_id: null,
      options: [
        'input',
        'textarea',
        'date',
        'file',
        'select',
        'radio',
        'checkbox',
        'custom_select',
      ].includes(type)
        ? [{ id: crypto.randomUUID(), label: 'Option 1', value: 'option1' }]
        : undefined,
      is_custom: true,
      is_disable: false,
      endpoint: '',
    };

    setCustomElements((prev) => [...prev, newElement]);
    setElements((prev) => [...prev, newElement]);
    setSelectedElement(newElement);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // Configure sensors for dnd-kit
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const onDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      setActiveId(null);
      return;
    }

    const oldIndex = elements.findIndex((element) => element.id === active.id);
    const newIndex = elements.findIndex((element) => element.id === over.id);

    if (oldIndex !== -1 && newIndex !== -1) {
      const newElements = arrayMove(elements, oldIndex, newIndex);

      const updatedElements = newElements.map((element, index) => ({
        ...element,
        order: index + 1,
      }));

      setElements(updatedElements);

      const ordersPayload = updatedElements
        .filter((element) => !isNaN(parseInt(element.id)))
        .map((element) => ({
          id: parseInt(element.id),
          order: element.order,
        }));

      try {
        if (ordersPayload.length > 0) {
          await updateQuestionOrder(formBuilderData.id, { orders: ordersPayload });
          toast.success(t('FrontEndErrorMessage.DSR.QuestionOrderUpdatedSuccessfully'));
        }
      } catch (error) {
        console.error('Failed to update question order:', error);
        toast.error('Failed to update question order. Please try again.');
      }
    }

    setActiveId(null);
  };
  const updateElement = (updatedElement: FormElement) => {
    setModifiedQuestions((prev) => new Set(prev).add(updatedElement.id));

    const isMasterElement = masterElements.some((el) => el.id === updatedElement.id);

    if (isMasterElement) {
      setMasterElements((prev) =>
        prev.map((el) => (el.id === updatedElement.id ? { ...el, ...updatedElement } : el))
      );
    } else {
      setCustomElements((prev) =>
        prev.map((el) => (el.id === updatedElement.id ? { ...el, ...updatedElement } : el))
      );
    }
    setElements((prev) =>
      prev.map((el) => (el.id === updatedElement.id ? { ...el, ...updatedElement } : el))
    );
    setSelectedElement(updatedElement);
  };

  const selectElement = (element: FormElement) => {
    // Select element without marking it as modified
    setSelectedElement(element);
  };

  const deleteElement = async (id: string) => {
    const elementToDelete = elements.find((el) => el.id === id);

    if (!elementToDelete) {
      toast.error('Question not found');
      return;
    }

    const isTemporaryElement = id.startsWith('temp-');

    if (isTemporaryElement) {
      setCustomElements((prev) => prev.filter((el) => el.id !== id));
      setElements((prev) => prev.filter((el) => el.id !== id));

      if (selectedElement?.id === id) {
        setSelectedElement(null);
      }

      toast.success(t('FrontEndErrorMessage.DSR.QuestionRemoved'));
      return;
    }

    const isCustomElement = elementToDelete.is_optional;

    if (isCustomElement) {
      try {
        await deleteDSRFormBuilderQuestion(id);

        setCustomElements((prev) => prev.filter((el) => el.id !== id));
        setElements((prev) => prev.filter((el) => el.id !== id));

        if (selectedElement?.id === id) {
          setSelectedElement(null);
        }

        toast.success(t('FrontEndErrorMessage.DSR.QuestionDeletedSuccessfully'));
      } catch (error) {
        console.error('Failed to delete question:', error);
        toast.error('Failed to delete question. Please try again.');
      }
    } else {
      toast.error('Master questions cannot be deleted');
    }
  };

  const handleInput = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setInputData(value);
  };

  const handleSave = async () => {
    if (!formBuilderData?.id) {
      toast.error('Form ID is required');
      return;
    }

    try {
      setIsSaving(true);

      const newCustomElements = customElements.filter((el) => el.id.startsWith('temp-'));
      let workingFormId = formBuilderData.id;

      // Check if form is published and we have new questions to add
      const isPublished =
        formBuilderData.published === 'Published' ||
        formBuilderData.published === 'YES' ||
        formBuilderData.published === 'true';

      if (newCustomElements.length > 0 && isPublished) {
        try {
          const currentContent = {
            logoUrl: logoUrl || logo,
            headerBackgroundColor: headerStyles.backgroundColor,
            textSize: 'medium',
            fontFamily: 'Poppins',
            fontColor: headerStyles.textColor,
            paragraphContent: headerDescription,
            headerTextColor: headerStyles.textColor,
            name: inputData,
            layoutPreference: layoutPreference,
            logoHeight: logoHeight,
            logoWidth: logoWidth,
          };
          const versionResponse = await createFormVersion(formBuilderData.id, currentContent);

          if (versionResponse.success && versionResponse.result?.id) {
            workingFormId = versionResponse.result.id.toString();
            console.log('New form version created with ID:', workingFormId);
            toast.success(t('ToastMessages.General.FormVersionCreatedSuccessfully'));
          } else {
            throw new Error(versionResponse.message || 'Failed to create form version');
          }
        } catch (versionError) {
          console.error('Failed to create form version:', versionError);
          toast.error('Failed to create form version. Please try again.');
          return;
        }
      }

      if (newCustomElements.length > 0) {
        const questionsPayload = {
          questions: newCustomElements.map((el) =>
            transformFormElementToControl(el, false, workingFormId)
          ),
        };
        const questionsResponse = await createQuestion(questionsPayload);

        if (!questionsResponse.success) {
          throw new Error(questionsResponse.message || 'Failed to save new questions');
        }

        if (questionsResponse.data?.questions) {
          const updatedElements = elements.map((el) => {
            if (el.id.startsWith('temp-')) {
              const match = questionsResponse.data.questions.find(
                (newEl: any) => newEl.title === el.label && newEl.artifact_type === el.type
              );
              if (match) {
                return {
                  ...el,
                  id: match.id.toString(),
                  question_id: match.question_id,
                };
              }
            }
            return el;
          });

          setElements(updatedElements);
          setCustomElements(
            updatedElements.filter((el) => !masterElements.some((mEl) => mEl.id === el.id))
          );
        }
      }

      const allElements = [...masterElements, ...customElements];
      const modifiedElements = allElements.filter(
        (el) => modifiedQuestions.has(el.id) && !el.id.startsWith('temp-')
      );

      if (modifiedElements.length > 0) {
        await Promise.all(
          modifiedElements.map(async (element) => {
            const payload = transformFormElementToControl(element, true);
            await updateDSRControls(element.id, payload);
          })
        );
      }

      const formContentData = {
        formId: workingFormId, // Use working form ID
        formName: inputData,
        content: {
          logoUrl: logoUrl || logo,
          headerBackgroundColor: headerStyles.backgroundColor,
          textSize: 'medium',
          fontFamily: 'Poppins',
          fontColor: headerStyles.textColor,
          paragraphContent: headerDescription,
          headerTextColor: headerStyles.textColor,
          name: inputData,
          layoutPreference: layoutPreference,
          logoHeight: logoHeight,
          logoWidth: logoWidth,
        },
      };

      const formContentResponse = await submitDSRFormContent(formContentData);
      if (formContentResponse.status !== 200) {
        throw new Error('Failed to save form content');
      }

      // Refresh data using the working form ID
      const refreshResponse = await fetchBasicInfoControl(workingFormId);
      if (refreshResponse?.result?.rows) {
        const controls: CreateFieldControl[] = refreshResponse.result.rows;
        const transformedElements: FormElement[] = [];
        for (const control of controls.filter((c) => c !== null)) {
          transformedElements.push(await transformControlToFormElement(control));
        }

        setMasterElements(transformedElements);
        setElements(transformedElements);
        setCustomElements([]);
        setModifiedQuestions(new Set());
      }

      toast.success(t('ToastMessages.General.FormSavedSuccessfully'));
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Failed to save form:', error);
      toast.error(t('ToastMessages.General.FailedToSaveForm'));
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async () => {
    try {
      if (!getCustomerId || !formBuilderData?.id) {
        toast.error(t('DSR.FormBuilder.CustomerIDAndFormIDRequired'));
        return;
      }

      const formContentData = {
        formId: formBuilderData.id,
        formName: inputData,
        content: {
          logoUrl: logoUrl || logo,
          headerBackgroundColor: headerStyles.backgroundColor,
          textSize: 'medium',
          fontFamily: 'Poppins',
          fontColor: headerStyles.textColor,
          paragraphContent: headerDescription,
          headerTextColor: headerStyles.textColor,
          name: inputData,
          layoutPreference: layoutPreference,
          logoHeight: logoHeight,
          logoWidth: logoWidth,
          formRules: formRules,
          rule_applied: formRules.length > 0 ? '1' : '0',
        },
      };

      const formContentResponse = await submitDSRFormContent(formContentData);

      if (formContentResponse.status !== 200) {
        throw new Error(t('DSR.FormBuilder.FailedToSaveFormContent'));
      }

      const encryptedFormId = encryptId(formBuilderData.id);
      const encryptedCustomerId = encryptId(getCustomerId.toString());

      const currentLanguage = localStorage.getItem('language') || 'en';

      const baseUrl = `${GUEST_REQUEST_FORM.slice(0, -1)}${encryptedFormId}/${encryptedCustomerId}`;
      const generatedUrl = `${baseUrl}?language_code=${currentLanguage}`;

      setPublicUrl(generatedUrl);
      setTranslationModalOpen(true);
    } catch (error) {
      console.error('Failed to prepare form for publishing:', error);
      toast.error(t('DSR.FormBuilder.FailedToGeneratePublicURL'));
    }
  };

  const handleSaveConfirmation = async () => {
    try {
      await handleSave();
      setSaveModalOpen(false);
      navigate(DSR_FORM_BUILDER);
    } catch (error) {
      console.error('Error during save confirmation:', error);
    }
  };

  const handlePublishOnly = async () => {
    try {
      if (!getCustomerId || !formBuilderData?.id || !publicUrl) {
        toast.error(t('DSR.FormBuilder.CustomerIDAndFormIDRequired'));
        return;
      }

      const publishResponse = await publishDsrForm(formBuilderData.id, getCustomerId, publicUrl);

      if (!publishResponse.success) {
        throw new Error(
          publishResponse.message || t('FrontEndErrorMessage.DSR.FailedToPublishForm')
        );
      }

      toast.success(t('DSR.FormBuilder.FormPublishedSuccessfully'));

      const updatedFormData = {
        ...formBuilderData,
        published: 'Published',
        url: publicUrl,
      };
      dispatch(setFormBuilderData(updatedFormData));
      dispatch(setUpdatedNameFormBuilderTableData(updatedFormData));

      setTranslationModalOpen(false);
      navigate(DSR_FORM_BUILDER);
    } catch (error) {
      console.error('Failed to publish form:', error);
      toast.error(t('FrontEndErrorMessage.DSR..FailedToPublishForm'));
    }
  };

  const handleTranslateForm = async () => {
    try {
      if (!getCustomerId || !formBuilderData?.id || !publicUrl) {
        toast.error(t('DSR.FormBuilder.CustomerIDAndFormIDRequired'));
        return;
      }

      const publishResponse = await publishDsrForm(formBuilderData.id, getCustomerId, publicUrl);

      if (!publishResponse.success) {
        throw new Error(
          publishResponse.message || t('FrontEndErrorMessage.DSR.FailedToPublishForm')
        );
      }

      toast.success(t('DSR.FormBuilder.FormPublishedSuccessfully'));
      const updatedFormData = {
        ...formBuilderData,
        published: 'Published',
        url: publicUrl,
      };
      dispatch(setFormBuilderData(updatedFormData));
      dispatch(setUpdatedNameFormBuilderTableData(updatedFormData));

      setTranslationModalOpen(false);
      navigate(DSR_FORM_TRANSLATION, {
        state: {
          formId: formBuilderData?.id,
          publicUrl: publicUrl,
        },
      });
    } catch (error) {
      console.error('Failed to publish form:', error);
      toast.error(t('FrontEndErrorMessage.DSR.FailedToPublishForm'));
    }
  };

  useEffect(() => {
    if (contentData) {
      setLogoUrl(contentData.logoUrl);
      setLogoHeight(contentData.logoHeight || 60);
      setLogoWidth(contentData.logoWidth || 120);
      setHeaderStyles({
        backgroundColor: contentData.headerBackgroundColor,
        textColor: contentData.headerTextColor,
      });
      setHeaderDescription(contentData.paragraphContent);
    }
  }, [contentData]);

  return (
    <div className="flex max-h-[80vh] w-full flex-col overflow-hidden">
      <div className="flex flex-1 gap-4 overflow-hidden bg-gray-50">
        <LeftNewFormBuilder
          onLogoChange={handleLogoChange}
          initialHeaderDescription={headerDescription}
          onHeaderDescriptionChange={(description) => setHeaderDescription(description)}
          initialLogoUrl={logoUrl}
          logoHeight={logoHeight}
          onLogoHeightChange={(value) => setLogoHeight(value)}
          logoWidth={logoWidth}
          onLogoWidthChange={(value) => setLogoWidth(value)}
          activeTab={activeLeftTab}
          setActiveTab={setActiveLeftTab}
        />

        <div className="flex min-w-[50%] flex-1 flex-col overflow-hidden rounded-md bg-white">
          <div className="p-1">
            <div className="flex items-center justify-between">
              <div className="grid grid-cols-2">
                <Input
                  type="text"
                  id="FormBuilderName"
                  placeholder="Enter Flow Type"
                  value={inputData}
                  onChange={handleInput}
                  readOnly
                />
              </div>
              <h1 className="text-2xl font-bold text-gray-900"></h1>
              <div className="flex space-x-2">
                <div className="flex items-center rounded-md bg-gray-100 p-1">
                  <button
                    onClick={() => setLayoutPreference(1)}
                    className={`btn-background-effect flex items-center rounded-md px-3 py-2 ${
                      layoutPreference === 1
                        ? 'bg-primary text-white'
                        : 'bg-transparent text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Square className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setLayoutPreference(2)}
                    className={`flex items-center rounded-md px-3 py-2 ${
                      layoutPreference === 2
                        ? 'bg-primary text-white'
                        : 'bg-transparent text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Columns2 className="h-4 w-4" />
                  </button>
                </div>
                <button
                  onClick={() => setActiveView('editor')}
                  className={`rounded-md px-3 py-2 ${
                    activeView === 'editor'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  title="Edit"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setActiveView('preview')}
                  className={`rounded-md px-3 py-2 ${
                    activeView === 'preview'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  title="Preview"
                >
                  <Eye className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
          <Separator className="bg-gray-300" />

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={onDragEnd}
          >
            <div
              className="flex-1 overflow-y-auto overflow-x-hidden p-3"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              style={{ minHeight: '400px', maxWidth: '100%' }}
            >
              <div
                className="mb-2 flex items-center justify-between rounded-t-md p-4 text-white"
                style={{
                  backgroundColor: headerStyles?.backgroundColor,
                  color: headerStyles?.textColor,
                }}
              >
                <h2 className="text-lg font-semibold">{t('DSR.AssigneeModal.DSRForm')}</h2>
                <img
                  src={logoUrl}
                  alt="Logo"
                  style={{ height: `${logoHeight}px`, width: `${logoWidth}px`, maxWidth: '200px' }}
                  className="rounded-md"
                />
              </div>
              <div className="px-4 py-2 text-sm text-gray-600">{headerDescription}</div>
              {isLoading ? (
                <div className="mt-4 flex h-[80vh] items-start justify-center">
                  <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
                </div>
              ) : activeView === 'editor' ? (
                <div className="mx-auto w-full max-w-full">
                  <SortableContext
                    items={elements.map((el) => el.id)}
                    strategy={
                      layoutPreference === 1 ? verticalListSortingStrategy : rectSortingStrategy
                    }
                  >
                    {layoutPreference === 1 ? (
                      // Single column layout - simple vertical list
                      <div className="space-y-4">
                        {elements.map((element) => (
                          <SortableItem
                            key={element.id}
                            id={element.id}
                            element={element}
                            onDelete={deleteElement}
                            onSelect={setSelectedElement}
                            isSelected={selectedElement?.id === element.id}
                          />
                        ))}
                      </div>
                    ) : (
                      // Two column layout - use flex with proper wrapping
                      <div className="flex flex-wrap gap-4">
                        {elements.map((element) => (
                          <div key={element.id} className="w-full md:w-[calc(50%-0.5rem)]">
                            <SortableItem
                              id={element.id}
                              element={element}
                              onDelete={deleteElement}
                              onSelect={setSelectedElement}
                              isSelected={selectedElement?.id === element.id}
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </SortableContext>

                  {showUploadDocuments && (
                    <div className="mt-4">
                      <div
                        className={`${
                          layoutPreference === 1 ? 'w-full' : 'w-full md:w-[calc(50%-0.5rem)]'
                        }`}
                      >
                        <FormElementEditor
                          element={{
                            id: 'upload-documents',
                            order: elements.length + 1,
                            is_optional: false,
                            type: 'file',
                            label: 'Upload Documents',
                            placeholder: null,
                            required: true,
                            question: '',
                            helpText: '',
                            question_id: null,
                            options: [],
                            is_custom: false,
                            is_disable: false,
                            endpoint: '',
                          }}
                          onDelete={deleteElement}
                          onSelect={setSelectedElement}
                          isSelected={selectedElement?.id === 'upload-documents'}
                        />
                      </div>
                    </div>
                  )}
                  <div className="rounded-lg border-2 border-dashed border-gray-300 bg-white py-12 text-center">
                    <div className="flex flex-col items-center">
                      <p className="text-gray-500">
                        Drag and drop elements from the sidebar to start building your form
                      </p>
                      <Plus className="mt-2 rounded-lg border" />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mx-auto">
                  <FormPreview elements={elements} />
                </div>
              )}
            </div>

            <DragOverlay>
              {activeId ? (
                <div className="rotate-0 transform opacity-90">
                  <FormElementEditor
                    element={elements.find((el) => el.id === activeId)!}
                    onDelete={() => {}}
                    onSelect={() => {}}
                    isSelected={false}
                  />
                </div>
              ) : null}
            </DragOverlay>
          </DndContext>
        </div>
        <RightNewFormBuilder
          selectedElement={selectedElement}
          onElementUpdate={updateElement}
          onElementSelect={selectElement}
          onHeaderStyleChange={handleHeaderStyleChange}
          activeLeftTab={activeLeftTab}
          headerStyles={headerStyles}
          formElements={elements}
          showUploadDocuments={showUploadDocuments}
          setShowUploadDocuments={setShowUploadDocuments}
          formRules={formRules}
          setFormRules={handleFormRulesChange}
          hasUnsavedChanges={hasUnsavedChanges}
          transformElementToControl={transformFormElementToControl}
        />
      </div>

      <div className="sticky bottom-0 flex justify-end gap-3 rounded-md bg-gray-100 p-2">
        <Button className="bg-blue-gray-200" onClick={() => navigate(DSR_FORM_BUILDER)}>
          Back
        </Button>
        <Button
          className="bg-custom-primary text-white hover:bg-custom-primary"
          onClick={handlePublish}
          disabled={
            !formBuilderData?.id ||
            !getCustomerId ||
            formBuilderData?.published === 'Published' ||
            formBuilderData?.published === 'YES' ||
            formBuilderData?.published === 'true' ||
            publicUrl !== ''
          }
        >
          {t('DSR.AssigneeModal.PublishForm')}
        </Button>

        <Dialog open={saveModalOpen} onOpenChange={setSaveModalOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <div className="flex flex-col items-center space-y-4 p-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900">Save Form</h3>
                <p className="mt-2 text-sm text-gray-600">
                  On save, this will generate a new version of this form. Do you want to continue?
                </p>
              </div>
              <div className="flex w-full space-x-3">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setSaveModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  className="flex-1 bg-custom-primary text-white hover:bg-custom-primary"
                  onClick={handleSaveConfirmation}
                  disabled={isSaving}
                >
                  {isSaving ? 'Saving...' : 'Yes, Continue'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        <Dialog open={translationModalOpen} onOpenChange={setTranslationModalOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <div className="flex flex-col items-center space-y-4 p-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900">
                  {t('DSR.FormBuilder.TranslateForm')}
                </h3>
                <p className="mt-2 text-sm text-gray-600">
                  {t('DSR.FormBuilder.DoYouWantToTranslateThisForm')}
                </p>
              </div>
              <div className="flex w-full space-x-3">
                <Button variant="outline" className="flex-1" onClick={handlePublishOnly}>
                  {t('DSR.FormBuilder.NoPublishNow')}
                </Button>
                <Button
                  className="flex-1 bg-custom-primary text-white hover:bg-custom-primary"
                  onClick={handleTranslateForm}
                >
                  {t('DSR.FormBuilder.YesTranslateForm')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        <Button
          className="bg-custom-primary text-white hover:bg-custom-primary"
          onClick={() => {
            const isPublished =
              formBuilderData.published === 'Published' ||
              formBuilderData.published === 'YES' ||
              formBuilderData.published === 'true';

            if (isPublished) {
              setSaveModalOpen(true);
            } else {
              handleSave();
            }
          }}
          disabled={isSaving || !formBuilderData?.id}
        >
          {isSaving ? 'Saving...' : 'Save'}
        </Button>
      </div>
    </div>
  );
}

export default CreateForm;
