import { Info, Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import { Input } from '../../../../../@/components/ui/Common/Elements/Input/Input';
import { Label } from '../../../../../@/components/ui/Common/Elements/Label/Label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../@/components/ui/Common/Elements/Select/Select';
import { Separator } from '../../../../../@/components/ui/Common/Elements/Seperator/Seperator';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../../../../@/components/ui/Common/Elements/Tabs/Tabs';
import { MultiSelect } from '../../../../../@/components/ui/multi-select';
import { Switch } from '../../../../../@/components/ui/switch';
import { FormElement, RightNewFormBuilderProps } from '../../../../../types/data-subject-rights';
import { updateDSRControls } from '../../../../common/services/data-subject-request';

export default function RightNewFormBuilder({
  selectedElement,
  onElementUpdate,
  onElementSelect,
  onHeaderStyleChange,
  activeLeftTab,
  headerStyles,
  formElements,
  formRules,
  setFormRules,
  showUploadDocuments,
  setShowUploadDocuments,
  hasUnsavedChanges,
  transformElementToControl,
}: RightNewFormBuilderProps) {
  const { t } = useTranslation();
  const [headerBackgroundColor, setHeaderBackgroundColor] = useState<string>(
    headerStyles?.backgroundColor || '#000000'
  );
  const [headerTextColor, setHeaderTextColor] = useState<string>(
    headerStyles?.textColor || '#ffffff'
  );
  const [selectedOptions, setSelectedOptions] = useState<string>('');
  const [selectedShowOrHide, setSelectedShowOrHide] = useState<string>('');
  const [selectedQuestionList, setSelectedQuestionList] = useState<string[]>([]);
  const [showAddRuleForm, setShowAddRuleForm] = useState<boolean>(false);

  const handleRemoveRule = async (ruleIndex: number) => {
    if (!selectedElement) return;

    try {
      // Get the current element's rules
      const currentRules = selectedElement.rules || [];

      // Remove the rule at the specified index
      const updatedRules = currentRules.filter((_, index) => index !== ruleIndex);

      // Update the element with the new rules
      const updatedElement = {
        ...selectedElement,
        rules: updatedRules,
        rule_applied: updatedRules.length > 0,
      };

      // Transform element to control payload
      const payload = transformElementToControl(updatedElement, true) as any;

      // Update the rules in the payload
      payload.rules = updatedRules;
      payload.rule_applied = updatedRules.length > 0 ? '1' : '0';

      // Update the control in the backend
      await updateDSRControls(selectedElement.id, payload);

      // Update the local element state
      onElementUpdate(updatedElement);

      toast.success(t('DSR.FormBuilder.RuleRemovedSuccessfully'));
    } catch (error) {
      console.error('Failed to remove rule:', error);
      toast.error(t('FrontEndErrorMessage.ApiErrors.SomethingWentWrong'));
    }
  };

  const handleCreateRule = async (
    options: string,
    showOrHide: string,
    questionList: string[],
    questionID: string
  ) => {
    // Only create rules for backend elements that have permanent IDs (not temp IDs)
    if (questionID.startsWith('temp-')) {
      toast.error(t('DSR.FormBuilder.SaveFormBeforeCreatingRules'));
      return;
    }

    // Make sure we have all required fields
    if (!options || !showOrHide || questionList.length === 0) {
      toast.error(t('DSR.FormBuilder.FillAllRuleFields'));
      return;
    }

    // Make sure the questionList doesn't contain temporary IDs
    const validQuestionList = questionList.filter((id) => !id.startsWith('temp-'));

    if (validQuestionList.length === 0) {
      toast.error(t('DSR.FormBuilder.SelectValidFieldsForRule'));
      return;
    }

    const newRule = {
      questionID, // The ID of the element this rule belongs to
      options, // The selected option value that triggers the rule
      showOrHide: showOrHide as 'show' | 'hide', // Whether to show or hide fields when triggered
      questionList: validQuestionList, // The fields to show/hide when triggered
    };

    try {
      // Find the element to update with the rule
      const elementToUpdate = formElements.find((el) => el.id === questionID);
      if (!elementToUpdate) {
        toast.error(t('FrontEndErrorMessage.ApiErrors.NotFound'));
        return;
      }

      // Get existing rules for this element and add the new rule
      const existingRules = elementToUpdate.rules || [];
      const updatedRules = [...existingRules, newRule];

      // Update the element with the new rules
      const updatedElement = {
        ...elementToUpdate,
        rules: updatedRules,
        rule_applied: true,
      };

      // Transform element to control payload
      const payload = transformElementToControl(updatedElement, true) as any;

      // Manually add the rules and rule_applied to the payload since the transform function
      // might not have the latest rules in the global state
      payload.rules = updatedRules;
      payload.rule_applied = '1';

      // Update the control in the backend immediately
      await updateDSRControls(questionID, payload);

      // Update the local element state
      onElementUpdate(updatedElement);

      // Reset the form after successful rule creation
      setSelectedOptions('');
      setSelectedShowOrHide('');
      setSelectedQuestionList([]);
      setShowAddRuleForm(false);

      toast.success(t('DSR.FormBuilder.RulesAppliedSuccessfully'));
    } catch (error) {
      console.error('Failed to apply rule:', error);
      toast.error(t('FrontEndErrorMessage.ApiErrors.SomethingWentWrong'));
    }
  };

  useEffect(() => {
    if (headerStyles) {
      setHeaderBackgroundColor(headerStyles.backgroundColor);
      setHeaderTextColor(headerStyles.textColor);
    }
  }, [headerStyles]);

  useEffect(() => {
    if (!selectedElement && formElements.length > 0) {
      // Use onElementSelect to avoid marking the element as modified
      if (onElementSelect) {
        onElementSelect(formElements[0]);
      } else {
        onElementUpdate(formElements[0]);
      }
    }
  }, [selectedElement, formElements, onElementUpdate, onElementSelect]);

  // Reset form when selected element changes
  useEffect(() => {
    setSelectedOptions('');
    setSelectedShowOrHide('');
    setSelectedQuestionList([]);
    setShowAddRuleForm(false);
  }, [selectedElement?.id]);

  const handlePropertyChange = (property: keyof FormElement, value: any) => {
    if (selectedElement) {
      console.log('Property change:', { property, value, elementId: selectedElement.id });
      const updatedElement = { ...selectedElement, [property]: value };
      console.log('Updated element:', {
        id: updatedElement.id,
        placeholder: updatedElement.placeholder,
      });
      onElementUpdate(updatedElement);
    }
  };

  const handleHeaderBackgroundColorChange = (color: string) => {
    setHeaderBackgroundColor(color);
    onHeaderStyleChange({ backgroundColor: color, textColor: headerTextColor });
  };

  const handleHeaderTextColorChange = (color: string) => {
    setHeaderTextColor(color);
    onHeaderStyleChange({ backgroundColor: headerBackgroundColor, textColor: color });
  };

  const renderQuestionEditor = () => {
    if (!selectedElement) return null;

    switch (selectedElement.type) {
      case 'select':
      case 'radio':
      case 'checkbox':
      case 'custom_select':
        return (
          <>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Options</Label>
              <div className="space-y-2">
                {(selectedElement.options || []).map((option, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      disabled={selectedElement?.type == 'custom_select'}
                      value={option.label}
                      onChange={(e) => {
                        const newOptions = [...(selectedElement?.options || [])];
                        const newValue = e.target.value;
                        newOptions[index] = {
                          ...option,
                          label: newValue,
                          value: newValue || `option_${index + 1}`, // Fallback to prevent empty value
                        };
                        handlePropertyChange('options', newOptions);
                      }}
                      placeholder={`Option ${index + 1}`}
                      className="bg-gray-50"
                      title={
                        selectedElement?.type === 'custom_select'
                          ? 'Unable to edit these options'
                          : ''
                      }
                    />
                    {selectedElement?.type !== 'custom_select' && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-destructive hover:bg-destructive/10 hover:text-destructive/90"
                        onClick={async () => {
                          const newOptions = [...(selectedElement?.options || [])];
                          newOptions.splice(index, 1);

                          const updatedElement = {
                            ...selectedElement,
                            options: newOptions,
                          };
                          onElementUpdate(updatedElement);

                          try {
                            if (!selectedElement.id.startsWith('temp-')) {
                              const payload = transformElementToControl(updatedElement, true);

                              await updateDSRControls(selectedElement.id, payload);

                              toast.success(t('DSR.FormBuilder.OptionRemovedSuccessfully'));
                            }
                          } catch (error) {
                            console.error('Failed to remove option:', error);
                            toast.error(t('DSR.FormBuilder.FailedToRemoveOption'));
                            onElementUpdate(selectedElement);
                          }
                        }}
                      >
                        <Trash2 className="h-5 w-5" />
                      </Button>
                    )}
                  </div>
                ))}
                {selectedElement?.type !== 'custom_select' && (
                  <button
                    onClick={() => {
                      if (!selectedElement) return;

                      const currentOptions = selectedElement.options || [];
                      const optionNumber = currentOptions.length + 1;
                      const defaultValue = `option_${optionNumber}`;
                      const newOptions = [
                        ...currentOptions,
                        {
                          id: crypto.randomUUID(),
                          label: `Option ${optionNumber}`,
                          value: defaultValue,
                        },
                      ];

                      handlePropertyChange('options', newOptions);
                    }}
                    className="hover:text-primary/80 text-sm text-primary"
                  >
                    + Add Option
                  </button>
                )}
              </div>
            </div>
          </>
        );

      default:
        return null;
    }
  };

  const renderFormElementProperties = () => {
    if (!selectedElement) return null;

    return (
      <div className="space-y-6">
        <div className="space-y-2">
          <Label className="text-sm font-medium">Field Label</Label>
          <Input
            value={selectedElement.label}
            onChange={(e) => handlePropertyChange('label', e.target.value)}
            placeholder="Enter field label"
            className="bg-gray-50"
          />
        </div>

        {selectedElement.type === 'radio' ||
        // selectedElement.type === 'number' ||
        selectedElement.type === 'file' ||
        selectedElement.type === 'date' ? (
          <></>
        ) : (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Placeholder Text</Label>
            <Input
              value={selectedElement?.placeholder || ''}
              onChange={(e) => handlePropertyChange('placeholder', e.target.value || null)}
              placeholder={
                selectedElement.type === 'select' || selectedElement.type === 'custom_select'
                  ? 'Select an option'
                  : 'Enter'
              }
              className="bg-gray-50"
            />
          </div>
        )}

        {renderQuestionEditor()}

        <div className="flex flex-col">
          <div className="flex flex-row justify-between">
            <Label className="text-sm font-medium">Required Field</Label>
            <Switch
              checked={selectedElement.required}
              onCheckedChange={(checked) => handlePropertyChange('required', checked)}
              disabled={!selectedElement.is_optional && !selectedElement.is_custom}
            />
          </div>
          {!selectedElement.is_optional && !selectedElement.is_custom && (
            <div className="flex items-start gap-2 py-1">
              <Info className="mt-0.5 h-4 w-4 text-red-500" />
              <p className="text-sm text-red-500">Master question can't be optional.</p>
            </div>
          )}
          {/* {selectedElement.required && (
            <div className="mt-2">
              <Label className="text-sm font-medium">Error Message</Label>
              <Input
                className="mt-1 bg-gray-50 text-sm text-gray-900"
                value={selectedElement.errorMessage || ''}
                onChange={(e) => handlePropertyChange('errorMessage', e.target.value)}
                placeholder="This field is required"
              />
            </div>
          )} */}
        </div>
      </div>
    );
  };

  const renderHeaderStylingProperties = () => {
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label className="text-sm font-medium">Header Background Color</Label>
          <div className="flex items-center gap-2">
            <Input
              type="text"
              value={headerBackgroundColor}
              onChange={(e) => handleHeaderBackgroundColorChange(e.target.value)}
              className="bg-gray-50"
            />
            <Input
              type="color"
              value={headerBackgroundColor}
              onChange={(e) => handleHeaderBackgroundColorChange(e.target.value)}
              className="h-9 w-9 bg-gray-50 p-0"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">Header Text Color</Label>
          <div className="flex items-center gap-2">
            <Input
              type="text"
              value={headerTextColor}
              onChange={(e) => handleHeaderTextColorChange(e.target.value)}
              className="bg-gray-50"
            />
            <Input
              type="color"
              value={headerTextColor}
              onChange={(e) => handleHeaderTextColorChange(e.target.value)}
              className="h-9 w-9 bg-gray-50 p-0"
            />
          </div>
        </div>
      </div>
    );
  };
  // Get rules for the currently selected element from the element's rules property
  const elementRules = selectedElement?.rules || [];

  return (
    <div className="w-full overflow-auto rounded-md bg-white py-2 lg:w-80">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger
            value="basic"
            className="data-[state=active]:bg-primary data-[state=active]:text-white"
          >
            Properties
          </TabsTrigger>
          <TabsTrigger
            className="data-[state=active]:bg-primary data-[state=active]:text-white"
            value="elements"
          >
            Logic
          </TabsTrigger>
        </TabsList>
        <TabsContent value="basic" className="space-y-6">
          <Separator className="bg-gray-300" />
          <div className="px-3">
            {activeLeftTab === 'basic'
              ? renderHeaderStylingProperties()
              : renderFormElementProperties()}
          </div>
        </TabsContent>
        <TabsContent value="elements" className="space-y-6 p-3">
          <Separator className="bg-gray-300" />
          <div className="space-y-4">
            {selectedElement?.type == 'radio' ||
            selectedElement?.type == 'select' ||
            selectedElement?.type == 'custom_select' ? (
              <>
                {/* Existing Rules Section */}
                {elementRules && elementRules.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-semibold text-gray-700">
                        Applied Rules ({elementRules.length})
                      </Label>
                    </div>
                    {elementRules.map((rule, index) => {
                      // Find the field labels for better display
                      const affectedFields =
                        rule.questionList
                          ?.map((fieldId) => {
                            const field = formElements.find((el) => el.id === fieldId);
                            return field?.label || fieldId;
                          })
                          .join(', ') || 'No fields selected';

                      // Find the option label by matching the rule.options value with the element's options
                      const optionLabel =
                        selectedElement?.options?.find((option) => option.value === rule.options)
                          ?.label ||
                        rule.options ||
                        'Unknown option';

                      return (
                        <div
                          key={`rule-${index}`}
                          className="rounded-md border border-gray-200 bg-gray-50 p-3"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 space-y-1">
                              <div className="text-sm">
                                <span className="font-medium">When</span>{' '}
                                <span className="rounded font-bold text-blue-800">
                                  {optionLabel}
                                </span>{' '}
                                <span className="font-medium">is selected</span>
                              </div>
                              <div className="text-sm">
                                <span className="font-medium">Then</span>{' '}
                                <span
                                  className={`rounded font-bold ${
                                    rule.showOrHide === 'show' ? 'text-green-800' : 'text-red-800'
                                  }`}
                                >
                                  {rule.showOrHide || 'unknown'}
                                </span>{' '}
                              </div>
                              <div className="pt-1 text-base text-gray-600">
                                <span className="">Fields : </span>

                                {affectedFields}
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveRule(index)}
                              className="h-8 w-8 text-red-500 hover:bg-red-50 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                    <Separator className="my-4" />
                  </div>
                )}

                {/* Add New Rule Section */}
                {!showAddRuleForm ? (
                  <Button
                    variant="outline"
                    onClick={() => setShowAddRuleForm(true)}
                    className="w-full border-dashed border-gray-300 text-gray-600 hover:border-custom-primary hover:text-custom-primary"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add New Rule
                  </Button>
                ) : (
                  <div className="space-y-3 rounded-md border border-custom-primary p-4">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-semibold text-custom-primary">
                        Create New Rule
                      </Label>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setShowAddRuleForm(false);
                          setSelectedOptions('');
                          setSelectedShowOrHide('');
                          setSelectedQuestionList([]);
                        }}
                        className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
                      >
                        ×
                      </Button>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <Label className="mb-1 block text-xs text-gray-600">
                          When this option is selected:
                        </Label>
                        <Select
                          onValueChange={(value) => setSelectedOptions(value)}
                          value={selectedOptions}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select an option" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {selectedElement?.options?.map((option) => (
                                <SelectItem key={option.id} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="mb-1 block text-xs text-gray-600">Then:</Label>
                        <Select
                          onValueChange={(value) => setSelectedShowOrHide(value)}
                          value={selectedShowOrHide}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Show or Hide" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="show">Show fields</SelectItem>
                              <SelectItem value="hide">Hide fields</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="mb-1 block text-xs text-gray-600">
                          Select fields to show/hide:
                        </Label>
                        <MultiSelect
                          options={
                            formElements
                              ?.filter(
                                (input) =>
                                  input?.is_optional &&
                                  !input.id.startsWith('temp-') &&
                                  input.id !== selectedElement?.id
                              )
                              .map((input) => ({
                                label: input.label,
                                value: input.id,
                              })) || []
                          }
                          onValueChange={(value) => setSelectedQuestionList(value)}
                          placeholder="Select fields"
                          value={selectedQuestionList}
                        />
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setShowAddRuleForm(false);
                            setSelectedOptions('');
                            setSelectedShowOrHide('');
                            setSelectedQuestionList([]);
                          }}
                          className="px-4"
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={() =>
                            handleCreateRule(
                              selectedOptions,
                              selectedShowOrHide,
                              selectedQuestionList,
                              selectedElement.id
                            )
                          }
                          className="flex-1 bg-custom-primary text-white hover:bg-custom-primary/90"
                          disabled={
                            !selectedOptions ||
                            !selectedShowOrHide ||
                            selectedQuestionList.length === 0 ||
                            selectedElement?.id?.startsWith('temp-')
                          }
                        >
                          Apply Rule
                        </Button>
                      </div>

                      {hasUnsavedChanges && selectedElement?.id?.startsWith('temp-') && (
                        <p className="mt-2 text-xs text-red-500">Save form first to apply rules</p>
                      )}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="mb-3 rounded-full bg-gray-100 p-3">
                  <Plus className="h-6 w-6 text-gray-400" />
                </div>
                <p className="mb-1 text-sm text-gray-500">Conditional Logic Not Available</p>
                <p className="text-xs text-gray-400">
                  Rules can only be applied to Radio and Select fields
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
