import { ChangeEvent, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/Button';
import { Combobox } from '../../../../../@/components/ui/Common/Elements/Combobox/Combobox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '../../../../../@/components/ui/Common/Elements/Form/Form';
import { Input } from '../../../../../@/components/ui/Common/Elements/Input/Input';
import edit from '../../../../../assets/images/edit_new.png';
import { RootState } from '../../../../../redux/store';
import {
  <PERSON>pi<PERSON><PERSON>r,
  DsrDashboardEntityProperties,
  EditWorkFlowFormData,
} from '../../../../../types/data-subject-rights';
import { DSR_WORKFLOW_TABLE } from '../../../../../utils/routeConstant';
import { editWorkflow } from '../../../../common/services/data-subject-request';
import { get_entities } from '../../../../common/services/universal-consent-management';
import Modal from '../../../../UI/Modal';
import { DsrEditWorkflowStepper } from '../../Common/dsr-workflow-stepper';
import CancelModal from '../../Common/Modal/cancel-modal';
import ConfirmModal from '../../Common/Modal/confirm-modal';

const DsrEditWorkflow = () => {
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  // const { name, id } = location?.state || {};
  const { workFlow, group_id: initialGroupId } = location?.state || {};
  const name = useSelector((state: RootState) => state.dataSubjectRights.workflow.flowtype);
  const id = useSelector((state: RootState) => state.dataSubjectRights.workflow.id);
  const group_id = useSelector((state: RootState) => state.dataSubjectRights.workflow.group_id);

  const [workflowName, setWorkflowName] = useState(name);
  const [isSubmitModalOpen, setIsSubmitModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [workflowStatus, setWorkflowList] = useState('published');
  const [workflowStatusDraft, setWorkflowListDraft] = useState('draft');
  const [error, setError] = useState('');
  const [selectedEntityId, setSelectedEntityId] = useState<string>(
    initialGroupId ? String(initialGroupId) : '-1'
  );
  const [entities, setEntities] = useState<DsrDashboardEntityProperties[]>([]);

  const handleChangeWorkflowName = (event: ChangeEvent<HTMLInputElement>) => {
    setWorkflowName(event.target.value);
  };
  console.log(workflowStatus, 'workFlow');

  const form = useForm<EditWorkFlowFormData>({
    // resolver: zodResolver(formSchema),
    defaultValues: {
      workFlowName: workflowName || '',
    },
  });
  console.log(group_id, 'group_id');

  const handleInputChange = () => {
    const inputValue = form.getValues('workFlowName');
    console.log(inputValue, 'inputValue');
    const regexPattern = /^[A-Za-z][A-Za-z0-9_()\-\s,\/]*$/;
    // Check if input is not empty before validating.
    setWorkflowName(inputValue);
    if (!inputValue) return setError('This field is required');

    // First, ensure the first character is a letter.
    if (!/^[A-Za-z]/.test(inputValue)) {
      setError('First character must be a letter.');
    }
    // Next, validate that the entire string follows the allowed pattern.
    else if (!regexPattern.test(inputValue)) {
      setError(
        'Only letters, numbers, underscores, parentheses, hyphens, spaces, commas, and forward slashes are allowed.'
      );
    } else {
      setError('');
      setIsSubmitModalOpen(true);
    }
  };

  const handleSubmit = async () => {
    if (!id) {
      toast.error(t('FrontEndErrorMessage.DSR.WorkflowIdMissing'));
      return;
    }

    setIsLoading(true);
    toast.loading(t('FrontEndErrorMessage.DSR.Processing'));
    try {
      const response = await editWorkflow(id, workflowName, workflowStatus, selectedEntityId);

      if (!response.success) {
        throw new Error(response.message || 'Failed to update workflow');
      }

      toast.dismiss();
      toast.success(t('FrontEndErrorMessage.DSR.WorkflowUpdatedSuccessfully'));
      navigate(DSR_WORKFLOW_TABLE);
    } catch (error) {
      toast.dismiss();
      const apiError = error as ApiError;
      if (apiError.response && apiError.response.status === 405) {
        toast.error(t('DSR.InvalidAPIRoute'));
        console.error('API Error:', apiError.response.data);
      } else {
        toast.error(t('FrontEndErrorMessage.DSR.FailedToUpdateWorkflow'));
      }
      console.error('Error updating workflow:', apiError);
    } finally {
      setIsLoading(false);
    }
  };

  const submitAsDraft = async () => {
    if (!id) {
      toast.error(t('FrontEndErrorMessage.DSR.WorkflowIdMissing'));
      return;
    }

    setIsLoading(true);
    toast.loading(t('FrontEndErrorMessage.DSR.Processing'));
    try {
      const response = await editWorkflow(id, workflowName, workflowStatusDraft, selectedEntityId);

      if (!response.success) {
        throw new Error(response.message || 'Failed to update workflow');
      }

      toast.dismiss();
      toast.success(t('FrontEndErrorMessage.DSR.WorkflowUpdatedSuccessfully'));
      navigate(DSR_WORKFLOW_TABLE);
    } catch (error) {
      toast.dismiss();
      const apiError = error as ApiError;
      if (apiError.response && apiError.response.status === 405) {
        toast.error(t('FrontEndErrorMessage.DSR.InvalidAPIRoute'));
        console.error('API Error:', apiError.response.data);
      } else {
        toast.error(t('FrontEndErrorMessage.DSR.FailedToUpdateWorkflow'));
      }
      console.error('Error updating workflow:', apiError);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const fetchDomains = async () => {
      try {
        const responseData = await get_entities(customer_id);
        setEntities(responseData?.result?.rows);
        if (responseData?.result?.rows?.length === 1) {
          setSelectedEntityId(responseData?.result?.rows[0]?.id.toString());
        }
      } catch (error) {
        console.error(error);
      }
    };
    fetchDomains();
  }, [customer_id]);

  return (
    <>
      {isSubmitModalOpen && (
        <Modal
          open={isSubmitModalOpen}
          onClose={() => setIsSubmitModalOpen(false)}
          cssClass="w-[90%] sm:w-[75%] md:w-[60%] lg:w-[45%] xl:w-[35%] 2xl:w-[30%] max-w-[1300px] shrink-0 rounded-2xl bg-white shadow-[10px_20px_30px_0px_rgba(0,0,0,0.10)]"
        >
          <ConfirmModal
            type="update"
            setIsOpen={setIsSubmitModalOpen}
            handleSubmit={handleSubmit}
            submitAsDraft={submitAsDraft}
          />
        </Modal>
      )}
      {isCancelModalOpen && (
        <Modal
          open={isCancelModalOpen}
          onClose={() => setIsCancelModalOpen(false)}
          cssClass="w-[90%] sm:w-[75%] md:w-[60%] lg:w-[45%] xl:w-[35%] 2xl:w-[30%] max-w-[1300px] shrink-0 rounded-2xl bg-white shadow-[10px_20px_30px_0px_rgba(0,0,0,0.10)]"
        >
          <CancelModal setIsOpen={setIsCancelModalOpen} />
        </Modal>
      )}
      <div className="size-full rounded-md font-[Poppins]">
        <div className="items-flex-end flex flex-col gap-4 p-5">
          <div className="flex-start flex h-11 w-full justify-between self-stretch">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className="flex w-full justify-between"
              >
                <div className="w-fit">
                  <FormField
                    control={form.control}
                    name="workFlowName"
                    rules={{
                      required: 'This field is required',
                    }}
                    render={({ field }) => (
                      <FormItem className="mb-2 w-full">
                        <FormControl>
                          <Input
                            defaultValue={workflowName}
                            {...field}
                            onChange={(e) => {
                              field.onChange(e); // ✅ Ensures react-hook-form updates state
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault(); // Prevent the form from submitting when Enter is pressed
                              }
                            }}
                            placeholder="Enter Flow Type"
                            icon={edit}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <p className="text-red-500">{error}</p>
                </div>

                <div className="flex h-full items-center gap-5">
                  <Combobox
                    placeholder="Select business unit"
                    options={
                      entities?.map((entity) => ({
                        value: entity?.id.toString(),
                        label: entity?.name,
                      })) || []
                    }
                    value={selectedEntityId}
                    onChange={(value) => setSelectedEntityId(value)}
                    disabled={workFlow === 'published'}
                  />

                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setIsCancelModalOpen(true)}
                    disabled={isLoading}
                  >
                    {t('Common.Cancel')}
                  </Button>
                  <Button
                    type="button"
                    className="bg-custom-primary text-white hover:bg-custom-primary"
                    disabled={isLoading}
                    onClick={handleInputChange}
                  >
                    {isLoading ? 'Updating...' : t('Common.Submit')}
                  </Button>
                </div>
              </form>
            </Form>
          </div>

          <div className="flex w-full justify-between pt-6">
            <DsrEditWorkflowStepper isEditMode={true} workFlow={workFlow} />
          </div>
        </div>
      </div>
    </>
  );
};

export default DsrEditWorkflow;
