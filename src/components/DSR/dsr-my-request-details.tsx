import BulletList from '@tiptap/extension-bullet-list';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { ChevronDown, Download, Maximize2, Send } from 'lucide-react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../../@/components/ui/collapsible';
import { Button } from '../../@/components/ui/Common/Elements/Button/Button';
import { Separator } from '../../@/components/ui/Common/Elements/Seperator/Seperator';
import rightArrow from '../../assets/chevron-right.svg';
import deleteIcon from '../../assets/deleteIcon.svg';
import documents from '../../assets/documents.svg';
import { setCustomerId } from '../../redux/reducers/DSR/dsr-slice';
import { RootState } from '../../redux/store';
import { MyRequestDetailData } from '../../types/data-subject-rights';
import { getInitialsByName } from '../../utils/helperData';
import AvatarFrame from '../common/Avatar';
import { convertDateToHumanView } from '../common/CommonHelperFunctions';
import { fetchMyRequestDetails, sendEditorContent } from '../common/services/data-subject-request';
import EditorMenuBar from './editor-menubar';
import GuestViewHeader from './Navbar/GuestViewHeader';
import PreviewGuestRequestForm from './preview-guest-request-form';
import ExpandablePanel from './TaskOverview/ApprovedRequest/ExpandableLog';
const DsrMyRequestDetails = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const { type, number, email, date, id } = location.state || {};
  const [data, setData] = useState<MyRequestDetailData | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [fileURL, setFileURL] = useState('');
  const [prevCon, setPrevCon] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [openSections, setOpenSections] = useState<Record<number, boolean>>({});
  const token = useSelector((state: RootState) => state.dataSubjectRights.authToken);
  const getEmail = useSelector((state: RootState) => state.dataSubjectRights?.email);
  const getCustomerId = useSelector((state: RootState) => state.dataSubjectRights?.customer_id);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const { t } = useTranslation();

  const navigate = useNavigate();
  const handleBack = () => {
    navigate(-1);
  };

  const handleOpenPanel = () => {
    setIsPanelOpen(true);
  };

  const handleClosePanel = () => {
    setIsPanelOpen(false);
  };

  const handleToggle = (index: number) => {
    setOpenSections((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
      }),
      BulletList.configure({
        HTMLAttributes: {
          class: 'list-disc pl-6',
        },
      }),
      Underline,
      Placeholder.configure({
        placeholder: 'Write Something...',
      }),
    ],
    // content: '<p class="editor-placeholder">Write Something...</p>',
    editorProps: {
      handleKeyDown: (view, event) => {
        if (view.state.doc.textContent === 'Write Something...') {
          view.dispatch(view.state.tr.delete(0, view.state.doc.content.size));

          return true;
        }
        return false;
      },
    },
  });

  const handleSend = async () => {
    try {
      if (editor && data) {
        const content = editor.getHTML().trim();

        // Check if content is empty or just contains HTML tags with no real content
        if (!content || content === '<p></p>' || content === '<p><br></p>') {
          toast.error(t('FrontEndErrorMessage.DSR.MessageCannotBeEmpty'));
          return;
        }

        const requestData = {
          content: content,
          dsr_request_id: id,
          customer_id: data?.customer_id,
          ...(file && {
            documents: [
              {
                original_name: file?.name,
                url: fileURL,
              },
            ],
          }),
        };

        const result = await sendEditorContent(token, requestData);
        const prevData = await fetchMyRequestDetails(token, id);

        if (result.success) {
          toast.success(result.message);
          editor.commands.clearContent();
          setFile(null);
        } else {
          toast.error(t('FrontEndErrorMessage.DSR.RequestUnderVerification'));
        }
        setPrevCon(prevData.email);
      }
    } catch (error) {
      console.error('Error while sending editor content:', error);
    }
  };

  const handleFile = (file: File) => {
    setFile(file);
  };

  const removeFile = () => {
    setFile(null);
    if (editor) {
      editor.commands.setContent('<p>Write Something...</p>');
    }
    toast.success(t('FrontEndErrorMessage.DSR.DocumentDeletedSuccessfully'));
  };

  const groupedData = prevCon.reduce((acc: any, curr: any) => {
    const date = new Date(curr.createdAt).toISOString().split('T')[0];

    if (!acc[date]) {
      acc[date] = [];
    }

    acc[date].push(curr);

    return acc;
  }, {});
  const groupedDataArray = Object.entries(groupedData).map(([date, items]) => ({
    date,
    items,
  }));

  useEffect(() => {
    const loadMyRequestsDetails = async () => {
      try {
        const result = await fetchMyRequestDetails(token, id);
        setData(result);
        if (result?.email) {
          setPrevCon(result?.email);
        }
        if (result?.customer_id) {
          dispatch(setCustomerId(result?.customer_id));
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };
    loadMyRequestsDetails();
  }, []);

  useEffect(() => {
    setOpenSections({ 0: true });
  }, [prevCon]);

  const openInNewTab = (url: string) => {
    window.open(url, 'noopener,noreferrer');
  };

  return (
    <div className="overflow-y-scroll">
      <div>
        <ExpandablePanel isOpen={isPanelOpen} onClose={handleClosePanel} title="Form Preview">
          <div className="flex flex-col items-center">
            <div className="min-w-[75vw] rounded-md border border-gray-300">
              <PreviewGuestRequestForm />
            </div>
          </div>
        </ExpandablePanel>
      </div>

      <GuestViewHeader />
      <div className="min-h-[90vh] p-5">
        <div className="mb-6 flex items-center justify-between">
          <div className={`flex flex-row items-center justify-start gap-[12px]`}>
            <div className="flex flex-row items-center justify-center gap-[26px]">
              <img
                onClick={handleBack}
                src={rightArrow}
                alt="right arrow sign"
                className="size-6 shrink-0 cursor-pointer rounded-full bg-black"
              />
            </div>
            <h1 className="text-[22px] font-semibold">
              Data Subject Request Details ({data?.dsr_id})
            </h1>
          </div>
          {/* <ImageButton
            className="flex h-10 w-auto justify-end gap-2.5 rounded-lg bg-ucm-blue px-2.5 py-2.5"
            onClick={() =>
              openInNewTab(`${ASSIGNEE_REQUEST_FORM.slice(0, -1)}${encryptId(getCustomerId)}`)
            }
          >
            <img src={buttonPlusIcon} alt="create ticket icon"></img>
            <p className="font-['Poppins'] text-xs font-medium text-white">Create New Request</p>
          </ImageButton> */}
        </div>
        <div className="flex h-full w-full items-center justify-center">
          <div className="w-3/4">
            <div className="h-[calc(80vh)] p-6">
              <div className="mb-8 rounded-lg border-2 border-gray-300 p-10 shadow-lg">
                <div className="mb-6 flex items-center">
                  <span id="name-span" className="text-[32px] font-medium text-ucm-blue">
                    {data?.DataSubject?.first_name} {data?.DataSubject?.last_name}
                  </span>
                  <Separator
                    className="h-[2px] flex-1 bg-[#E2E2E2]"
                    style={{
                      marginLeft: `calc(${document.getElementById('name-span')?.offsetWidth}px + 16px)`,
                    }}
                  />
                </div>

                <div className="mb-6">
                  <div className="">
                    <div className="grid grid-cols-5 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Request ID</p>
                        <p className="font-medium leading-8">{data?.dsr_id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Request Type</p>
                        <p className="font-medium leading-8">{data?.DsrRequestType?.flowtype}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Request Date</p>
                        <p className="font-medium leading-8">
                          {data?.created_date !== null
                            ? convertDateToHumanView(data?.created_date)
                            : '-'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Tentative Completion Date</p>
                        <p className="font-medium leading-8">
                          {data?.deadline_date !== null
                            ? convertDateToHumanView(data?.deadline_date)
                            : '-'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Region</p>
                        <div className="flex items-center">
                          <p className="font-medium leading-8">
                            {data?.DataSubject?.country?.country_name}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <Separator className="my-5 bg-[#C0CDE0]" />
                <div>
                  <div>
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Request Form</p>
                        <p className="text-sm font-medium leading-8">Form Preview</p>
                        <Maximize2
                          className="ml-2 h-4 w-4 cursor-pointer"
                          onClick={handleOpenPanel}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-5 bg-[#C0CDE0]" />
              </div>

              {/* {data?.assigned_date && ( */}
              <div className="">
                <div className="border-blue-gray-100 rounded-md border">
                  <div className="p-2">
                    <div className="flex items-center justify-between">
                      <EditorMenuBar
                        editor={editor}
                        onFileSelect={handleFile}
                        setFileURL={setFileURL}
                      />
                      <Button
                        className="flex items-center gap-1 bg-custom-primary text-white hover:bg-custom-primary"
                        onClick={handleSend}
                      >
                        Send
                        <Send className="h-3 w-3 fill-current text-white" />
                      </Button>
                    </div>
                    <div className="min-h-[100px] max-w-none rounded-md border border-gray-300 bg-white focus-within:ring-1">
                      <EditorContent
                        editor={editor}
                        className="p-2 [&_.ProseMirror]:min-h-[80px] [&_.ProseMirror]:!border-none [&_.ProseMirror]:!outline-none"
                      />
                    </div>
                    {/* File Display Section */}
                    {file && (
                      <div className="mt-4 flex items-center gap-2 rounded bg-white p-1">
                        <img src={documents} className="size-6" alt="Document" />
                        <span className="text-sm">
                          {file?.name.split('.').slice(0, -1).join('.')}
                        </span>
                        <img
                          src={deleteIcon}
                          className="ml-2 size-4 cursor-pointer"
                          alt="Delete"
                          onClick={removeFile}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {/* )} */}
              {/* {data?.assigned_date && ( */}
              <div className="mt-8">
                <h2 className="font-bold">Previous Conversation</h2>
                <div className="p-0">
                  <div className="w-full rounded-md">
                    {groupedDataArray.length > 0 ? (
                      groupedDataArray.map((convo: any, index) => (
                        <div key={convo.date || index} className="p-0">
                          <Collapsible
                            open={openSections[index]}
                            onOpenChange={() => handleToggle(index)}
                          >
                            <CollapsibleTrigger className="w-full">
                              <div className="my-4 flex items-center">
                                <div className="grow border-t border-gray-300"></div>
                                <span className="mx-4 flex flex-row items-center gap-1 rounded-full border border-gray-400 p-1 text-center text-sm text-gray-500">
                                  {convo?.date !== null ? convertDateToHumanView(convo?.date) : '-'}
                                  <ChevronDown
                                    className={`h-4 w-4 text-sm text-gray-500 transition-transform duration-200 ${
                                      openSections[index] ? 'rotate-180' : ''
                                    }`}
                                  />
                                </span>
                                <div className="grow border-t border-gray-300"></div>
                              </div>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                              {convo?.items.map((item: any) => (
                                <div
                                  key={item.id}
                                  className={
                                    item.subject === null
                                      ? 'mb-4 rounded-lg border border-gray-300 bg-[rgb(242,242,255)] p-3'
                                      : 'mb-4 rounded-lg border border-gray-300 bg-gray-100 p-3'
                                  }
                                >
                                  <div className="mb-1 flex items-center gap-2">
                                    <span>
                                      <AvatarFrame
                                        value={`${item?.first_name} ${item?.last_name}`}
                                        getInitials={getInitialsByName}
                                      />
                                    </span>
                                    <span className="font-medium">
                                      {item?.first_name} {item?.last_name}
                                    </span>
                                    <div className="text-xs font-semibold text-gray-500">
                                      {item.createdAt
                                        ? `${convertDateToHumanView(item?.createdAt)} | ${new Date(item?.createdAt).toLocaleTimeString()}`
                                        : '-'}
                                    </div>
                                  </div>
                                  <div className="rounded-md">
                                    <div
                                      className="rounded-md"
                                      dangerouslySetInnerHTML={{ __html: item?.content || '' }}
                                    />
                                    {item?.DsrMailDocuments &&
                                    item?.DsrMailDocuments?.length > 0 ? (
                                      <div
                                        className="mt-2 flex items-center gap-2 rounded bg-white p-1"
                                        style={{ width: 'max-content' }}
                                      >
                                        <img src={documents} className="size-6" alt="Document" />
                                        <span className="text-sm">
                                          {item?.DsrMailDocuments[0]?.original_name
                                            .split('.')
                                            .slice(0, -1)
                                            .join('.')}
                                        </span>
                                        <a
                                          href={item?.DsrMailDocuments[0]?.url}
                                          download={item?.DsrMailDocuments[0]?.original_name}
                                        >
                                          <Download className="ml-2 size-3 cursor-pointer text-indigo-600" />
                                        </a>
                                      </div>
                                    ) : (
                                      <p className="mt-2 text-sm text-gray-500"></p>
                                    )}
                                  </div>
                                </div>
                              ))}
                              {index < groupedDataArray.length - 1 && (
                                <Separator className="my-4" />
                              )}
                            </CollapsibleContent>
                          </Collapsible>
                        </div>
                      ))
                    ) : (
                      <p className="text-center text-gray-500">No conversation.</p>
                    )}
                  </div>
                </div>
              </div>
              {/* )} */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DsrMyRequestDetails;
