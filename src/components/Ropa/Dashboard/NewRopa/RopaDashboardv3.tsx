import { useQuery } from '@tanstack/react-query';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Clock, Eye, FileText } from 'lucide-react';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Badge } from '../../../../@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import httpClient from '../../../../api/httpClient';
import { FETCH_ENTITIES } from '../../../common/api';

import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
import { Label } from '../../../../@/components/ui/Common/Elements/Label/Label';
import SearchableSelect from '../../../../@/components/ui/Common/Elements/Select/SearchableSelect';
import { ropaHooks } from '../../../../@/hooks/ropa-hooks';
import {
  setSelectedEntityId,
  setSelectedEntityName,
} from '../../../../redux/reducers/ActivityLog/ActivityLogSlice';
import { ropaRegisterActions } from '../../../../redux/reducers/Ropa/ropaRegisterSlice';
import { RootState } from '../../../../redux/store';
import { ACTIVITY_LOG } from '../../../../utils/routeConstant';
import { fetchRopaStats } from '../../../common/services/ropaDashboard';
import { ActiveCollaborator } from './ActiveCollaborator';
import NewOrganizationalRole from './NewOrganizationRole';
import { RiskDistribution } from './RiskDistribution';
import ThirdPartiesList from './ThirdPartiesList';

export function ROPADashboard() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const customer_id = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const selectedEntityId = useSelector((state: RootState) => state?.activityLog?.selectedEntityId);

  const { data: entities } = useQuery({
    queryKey: ['entities', customer_id],
    queryFn: async () => {
      if (!customer_id) return [];
      const response = await httpClient.get(`${FETCH_ENTITIES}${customer_id}`);
      return response.data.result.rows;
    },
    enabled: !!customer_id,
  });

  // Initialize selectedEntityId when entities are loaded and no entity is selected
  useEffect(() => {
    if (entities && entities.length > 0 && selectedEntityId === -1) {
      const firstEntity = entities[0];
      dispatch(setSelectedEntityId(firstEntity.id));
      dispatch(setSelectedEntityName(firstEntity.name));
      dispatch(ropaRegisterActions.setSelectedEntityId(firstEntity.id));
    }
  }, [entities, selectedEntityId, dispatch]);

  const effectiveEntityId =
    selectedEntityId && selectedEntityId !== -1 ? selectedEntityId : entities?.[0]?.id;

  const { data: ropaStatsData1 } = useQuery({
    queryKey: ['ropaStats', effectiveEntityId],
    queryFn: fetchRopaStats,
    enabled: !!effectiveEntityId,
  });
  const {
    data: ropaStatsData,
    isLoading: isRopaStatsDataLoading,
    error: ropaStatsDataError,
  } = ropaHooks.useFetchRopaDataCount(effectiveEntityId);
  const {
    recentData: recentRopaData,
    isLoading: isRecentRopaLoading,
    error: recentDataError,
  } = ropaHooks.useFetchRecentRopa(effectiveEntityId);
  const getCount = (type: string) => {
    return ropaStatsData?.data?.result?.find((item: any) => item.ropa === type)?.count || '0';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'Under Review':
        return <Eye className="h-4 w-4 text-orange-500" />;
      case 'Started':
      case 'In Progress':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'Change Request':
        return <AlertTriangle className="h-4 w-4 text-white" />;
      case 'Yet to Start':
        return <AlertTriangle className="h-4 w-4 text-slate-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-slate-500" />;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'default';
      case 'Under Review':
        return 'secondary';
      case 'Started':
      case 'In Progress':
        return 'outline';
      case 'Change Request':
        return 'destructive';
      case 'Yet to Start':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const statsConfig = [
    { label: t('Ropa.Dashboard.ROPAV3.Total ROPAs'), type: 'TOTAL', icon: FileText, color: 'blue' },
    {
      label: t('Ropa.Dashboard.ROPAV3.Yet to Start'),
      type: 'YET_TO_START',
      icon: AlertTriangle,
      color: 'red',
    },
    {
      label: t('Ropa.Dashboard.ROPAV3.In Progress'),
      type: 'IN_PROGRESS',
      icon: Clock,
      color: 'yellow',
    },
    {
      label: t('Ropa.Dashboard.ROPAV3.Completed'),
      type: 'COMPLETED',
      icon: CheckCircle,
      color: 'green',
    },
  ];

  const formatRecentROPAs = (apiData: any[]) => {
    return (
      apiData?.map((ropa: any) => ({
        id: `ROPA-${ropa.id}`,
        name: `${ropa.Department?.name || 'Unknown Department'}`,
        status: ropa.status || 'Unknown',
        assignee: ropa.AssignedTo?.name || 'Unassigned',
        dueDate: ropa.tentative_completion_date
          ? new Date(ropa.tentative_completion_date).toLocaleDateString()
          : 'No due date',
        progress: ropa.progress || 0,
      })) || []
    );
  };
  const recentROPAs = formatRecentROPAs(recentRopaData?.data?.result?.rows || []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {' '}
            {t('Ropa.Dashboard.ROPAV3.ROPA Management Dashboard')}
          </h2>
          <p className="text-slate-600">
            {t(
              'Ropa.Dashboard.ROPAV3.Records of Processing Activities (Article 30 GDPR Compliance)'
            )}
          </p>
        </div>
        <div>
          <Label className="text-md font-medium">Select Entity</Label>
          <SearchableSelect
            className="h-10 w-[180px] rounded-md border border-solid border-[#CACACA] bg-white p-1 px-2"
            placeholder="Select Entity"
            value={selectedEntityId?.toString()}
            onChange={(selectedValue) => {
              const entityId = Number(selectedValue);
              const entityName = entities?.find((item: any) => item?.id === entityId)?.name;
              dispatch(setSelectedEntityId(entityId));
              dispatch(ropaRegisterActions.setSelectedEntityId(entityId));
              dispatch(setSelectedEntityName(entityName));
            }}
            options={(entities || []).map((entity: any) => ({
              id: entity.id.toString(),
              value: entity.name,
            }))}
          />
        </div>
      </div>

      {/* Stats Overview */}
      <div className="mt-2 grid grid-cols-1 gap-6 md:grid-cols-4">
        {statsConfig.map((stat, index) => (
          <Card key={index} className="shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">{stat.label}</p>
                  <p className="text-2xl font-bold">{getCount(stat.type)}</p>
                  <p className="text-xs text-slate-600">
                    {t('Ropa.Dashboard.ROPAV3.Active assessments')}
                  </p>
                </div>
                <stat.icon className={`h-8 w-8 text-${stat.color}-500`} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-2 grid auto-rows-fr grid-cols-2 gap-6 md:grid-cols-2">
        <NewOrganizationalRole />
        <RiskDistribution />
        <ThirdPartiesList />
        <ActiveCollaborator entity_id={effectiveEntityId} />{' '}
      </div>

      {/* Recent ROPAs */}
      <Card className="shadow-md">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{t('Ropa.Dashboard.ROPAV3.Recent ROPA Activities')}</CardTitle>
            <CardDescription>
              {t('Ropa.Dashboard.ROPAV3.Latest updates on processing activity assessments')}
            </CardDescription>
          </div>
          <div>
            <Button variant="outline" onClick={() => navigate(`${ACTIVITY_LOG}`)}>
              {t('Ropa.Dashboard.ROPAV3.View All')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isRecentRopaLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-slate-600">
                {t('Ropa.Dashboard.ROPAV3.Loading recent activities...')}
              </div>
            </div>
          ) : recentROPAs.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-slate-600">
                {t('Ropa.Dashboard.ROPAV3.No recent ROPA activities found')}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {recentROPAs.map((ropa, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg border p-4"
                >
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-semibold">{ropa.name}</span>
                      <Badge variant="outline">{ropa.id}</Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-slate-600">
                      <span>
                        {t('Ropa.Dashboard.ROPAV3.Assignee')}: {ropa.assignee}
                      </span>
                      {ropa.progress !== undefined && (
                        <span>
                          {t('Ropa.Dashboard.ROPAV3.Progress')}: {ropa.progress}%
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={getStatusVariant(ropa.status)}
                      className="flex w-fit items-center gap-1"
                    >
                      {getStatusIcon(ropa.status)}
                      {ropa.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
