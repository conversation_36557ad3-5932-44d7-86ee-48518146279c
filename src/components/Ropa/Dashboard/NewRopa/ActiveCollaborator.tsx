import { useQuery } from '@tanstack/react-query';
import { AlertTriangle, Users } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import { Progress } from '../../../../@/components/ui/Common/Elements/ProgressBar/ProgressBar';
import { Badge } from '../../../../@/components/ui/badge';
import { Fetch_Ropa_Dashboard_Collaborator } from '../../../common/services/ropaDashboard';

interface RopaItem {
  ropa_id: number;
  ropa_name: string;
  status: string;
}

interface CollaboratorData {
  assignee_id: number;
  assignee_name: string;
  total_ropas: number;
  completed_ropas: number;
  completion_percentage: number;
  ropas: RopaItem[];
}

interface ActiveCollaboratorProps {
  entity_id: number;
}

export function ActiveCollaborator({ entity_id }: ActiveCollaboratorProps) {
  const { t } = useTranslation();
  const {
    data: collaborators = [],
    isLoading: loading,
    error,
  } = useQuery<CollaboratorData[]>({
    queryKey: ['ropaDashboardCollaborator', entity_id], // Include entity_id in queryKey
    queryFn: async () => {
      const response = await Fetch_Ropa_Dashboard_Collaborator(entity_id);
      if (response?.data?.success) {
        return response.data.result;
      }
      throw new Error(t('Ropa.Dashboard.ROPAV3.Failed to fetch collaborator data'));
    },
    enabled: !!entity_id, // Only fetch if entity_id is provided
  });

  if (error) {
    console.error('Error fetching collaborators:', error);
  }

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'default';
      case 'under review':
        return 'secondary';
      case 'in progress':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (loading) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex-1">
          <Card className="flex flex-col shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                {t('Ropa.Dashboard.ROPAV3.Active Collaborators')}
              </CardTitle>
              <CardDescription>
                {t('Ropa.Dashboard.ROPAV3.Team members working on ROPA assessments')}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex items-center justify-center p-8">
              <div className="text-center">
                <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
                <p className="text-sm text-slate-600">
                  {t('Ropa.Dashboard.ROPAV3.Loading collaborators...')}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex-1">
          <Card className="flex flex-col shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                {t('Ropa.Dashboard.ROPAV3.Active Collaborators')}
              </CardTitle>
              <CardDescription>
                {t('Ropa.Dashboard.ROPAV3.Team members working on ROPA assessments')}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex items-center justify-center p-8">
              <div className="text-center">
                <AlertTriangle className="mx-auto mb-2 h-8 w-8 text-red-500" />
                <p className="text-sm text-red-600">
                  {(error as Error)?.message ||
                    t('Ropa.Dashboard.ROPAV3.Failed to load collaborator data')}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1">
        <Card className="flex h-[450px] flex-col overflow-auto shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              {t('Ropa.Dashboard.ROPAV3.Active Collaborators')}
            </CardTitle>
            <CardDescription>
              {t('Ropa.Dashboard.ROPAV3.Team members working on ROPA assessments')}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-1 flex-col space-y-4 overflow-hidden">
            {collaborators.length === 0 ? (
              <div className="py-8 text-center">
                <Users className="mx-auto mb-4 h-12 w-12 text-slate-300" />
                <p className="text-slate-500">
                  {t('Ropa.Dashboard.ROPAV3.No active collaborators found')}
                </p>
              </div>
            ) : (
              <div className="flex-1 space-y-4 overflow-y-auto">
                {collaborators.map((collaborator) => (
                  <div key={collaborator.assignee_id} className="space-y-3 rounded-lg border p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{collaborator.assignee_name}</h4>
                        <p className="text-sm text-slate-600">
                          {collaborator.total_ropas} {t('Ropa.Dashboard.ROPAV3.ROPAs assigned')} •{' '}
                          {collaborator.completed_ropas} {t('Ropa.Dashboard.ROPAV3.completed')}
                        </p>
                      </div>
                      <Badge variant="outline">
                        {collaborator.completion_percentage}% {t('Ropa.Dashboard.ROPAV3.Complete')}
                      </Badge>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{t('Ropa.Dashboard.ROPAV3.Progress')}</span>
                        <span>{collaborator.completion_percentage}%</span>
                      </div>
                      <Progress
                        value={collaborator.completion_percentage}
                        color="bg-custom-primary"
                        className="h-2"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {collaborator.ropas.slice(0, 3).map((ropa) => (
                          <Badge
                            key={ropa.ropa_id}
                            variant={getStatusVariant(ropa.status)}
                            className="text-xs"
                          >
                            {ropa.ropa_name}
                          </Badge>
                        ))}
                        {collaborator.ropas.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{collaborator.ropas.length - 3} {t('Ropa.Dashboard.ROPAV3.more')}
                          </Badge>
                        )}
                      </div>
                      <span className="text-xs text-slate-500">
                        {collaborator.total_ropas} {t('Ropa.Dashboard.ROPAV3.total ROPAs')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
