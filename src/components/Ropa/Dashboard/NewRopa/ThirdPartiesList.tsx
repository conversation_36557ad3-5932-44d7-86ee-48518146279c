import { useQuery } from '@tanstack/react-query';
import { ChevronRight, List } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../../../../@/components/ui/Common/Elements/Accordian/Accordian';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/Common/Elements/Card/Card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../../@/components/ui/Common/Table/Table';
import { Fetch_Ropa_Dashboard_Third_Party } from '../../../common/services/ropaDashboard';

export default function ThirdPartiesList() {
  const { t } = useTranslation();
  const {
    data: ropaStatsData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['ropaThirdParty'],
    queryFn: Fetch_Ropa_Dashboard_Third_Party,
  });

  const total = ropaStatsData?.data?.result;
  const resultItems = ropaStatsData?.data?.result?.items || [];
  const grouped = resultItems.reduce(
    (acc: any, item: any) => {
      if (!item.departmentName) return acc;
      if (!acc[item.departmentName]) acc[item.departmentName] = [];
      acc[item.departmentName].push(item);
      return acc;
    },
    {} as Record<string, any[]>
  );

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1">
        <Card className="flex h-[450px] flex-col overflow-auto shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <List className="h-5 w-5 text-green-500" />
              {t('Ropa.Dashboard.ROPAV3.List of Third Parties by Department')}
            </CardTitle>
            <CardDescription>
              {t('Ropa.Dashboard.ROPAV3.Total count')} {total?.total}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-1 flex-col overflow-hidden">
            {isLoading ? (
              <div>{t('Ropa.Dashboard.ROPAV3.Loading...')}</div>
            ) : isError ? (
              <div className="text-red-500">{t('Ropa.Dashboard.ROPAV3.Failed to load data.')}</div>
            ) : (
              <div className="min-h-0 flex-1 overflow-y-auto">
                {Object.keys(grouped).length > 0 ? (
                  <Accordion
                    type="single"
                    className="space-y-2"
                    defaultValue={Object.keys(grouped).length > 0 ? Object.keys(grouped)[0] : ''}
                  >
                    {Object.entries(grouped).map(([dept, items]) => (
                      <AccordionItem
                        key={dept}
                        value={dept}
                        className="rounded-lg border border-gray-200"
                      >
                        <AccordionTrigger className="group rounded-t-lg px-2 hover:bg-gray-100 data-[state=open]:rounded-b-none [&>span>svg]:hidden [&>svg]:hidden">
                          <div className="flex w-full items-center justify-between">
                            <span className="font-medium">{dept}</span>
                            <ChevronRight className="h-5 w-5 transition-transform duration-200 group-data-[state=open]:rotate-90" />
                            {/* <ChevronDown  className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" /> */}
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="overflow-x-auto px-0 pb-0">
                          <Table>
                            <TableHeader>
                              <TableRow className="border-none transition-colors hover:bg-[#E1E1E1]/20 data-[state=selected]:bg-muted">
                                <TableHead className="text-black-700 px-4 py-3 text-left font-medium">
                                  {t('Ropa.Dashboard.ROPAV3.Vendor')}
                                </TableHead>
                                <TableHead className="text-black-700 px-4 py-3 text-left font-medium">
                                  {t('Ropa.Dashboard.ROPAV3.Services')}
                                </TableHead>
                                <TableHead className="text-black-700 px-4 py-3 text-left font-medium">
                                  {t('Ropa.Dashboard.ROPAV3.Department')}
                                </TableHead>
                                <TableHead className="text-black-700 px-4 py-3 text-left font-medium">
                                  {t('Ropa.Dashboard.ROPAV3.Location')}
                                </TableHead>
                                <TableHead className="text-black-700 px-4 py-3 text-left font-medium">
                                  {t('Ropa.Dashboard.ROPAV3.Personal Data Involved')}
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {(items as any[]).map((item: any) =>
                                item.answers && item.answers.length > 0 ? (
                                  item.answers.map((ans: any, idx: number) => (
                                    <TableRow
                                      key={item.id + '-' + idx}
                                      className="border-b border-gray-100"
                                    >
                                      <TableCell className="px-4 py-3">
                                        {ans.company || '-'}
                                      </TableCell>
                                      <TableCell className="px-4 py-3">
                                        {ans.businessType || '-'}
                                      </TableCell>
                                      <TableCell className="px-4 py-3">
                                        {ans.department || '-'}
                                      </TableCell>
                                      <TableCell className="px-4 py-3">
                                        {ans.location || '-'}
                                      </TableCell>
                                      <TableCell className="px-4 py-3">
                                        {ans.personalData || '-'}
                                      </TableCell>
                                    </TableRow>
                                  ))
                                ) : (
                                  <TableRow key={item.id} className="border-b border-gray-100">
                                    <TableCell colSpan={5} className="px-4 py-3 text-gray-500">
                                      {t('Ropa.Dashboard.ROPAV3.No data available')}
                                    </TableCell>
                                  </TableRow>
                                )
                              )}
                            </TableBody>
                          </Table>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                ) : (
                  <div className="overflow-x-auto px-8 pb-8">
                    {t('Ropa.Dashboard.ROPAV3.No records found.')}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
