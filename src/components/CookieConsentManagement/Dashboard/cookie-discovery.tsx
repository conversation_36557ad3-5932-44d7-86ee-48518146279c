import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Badge } from '../../../@/components/ui/badge';
import { CookieDictionary } from '../CookieDictionary/cookie-dictionary';

export function CookieDiscovery() {
  const discoveredCookies = [
    {
      name: '_ga',
      domain: 'example.com',
      category: 'Analytics',
      vendor: 'Google Analytics',
      type: 'Third-party',
      expiration: '2 years',
      httpOnly: false,
      secure: true,
      purpose: 'Used to distinguish users',
      lastSeen: '2024-01-15',
      classification: 'Automatic',
    },
    {
      name: 'session_id',
      domain: 'example.com',
      category: 'Necessary',
      vendor: 'Internal',
      type: 'First-party',
      expiration: 'Session',
      httpOnly: true,
      secure: true,
      purpose: 'Session management',
      lastSeen: '2024-01-15',
      classification: 'Manual',
    },
    {
      name: '_fbp',
      domain: 'facebook.com',
      category: 'Marketing',
      vendor: 'Facebook',
      type: 'Third-party',
      expiration: '90 days',
      httpOnly: false,
      secure: true,
      purpose: 'Facebook pixel tracking',
      lastSeen: '2024-01-14',
      classification: 'AI-Powered',
    },
  ];

  const scanResults = [
    {
      domain: 'example.com',
      lastScan: '2024-01-15 10:30',
      status: 'Completed',
      cookiesFound: 42,
      newCookies: 3,
      changes: 1,
      progress: 100,
    },
    {
      domain: 'subdomain.example.com',
      lastScan: '2024-01-15 09:45',
      status: 'In Progress',
      cookiesFound: 28,
      newCookies: 0,
      changes: 0,
      progress: 75,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4 mt-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Necessary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">12</div>
            <p className="text-sm text-slate-600">Essential cookies</p>
            <div className="mt-2">
              <Badge variant="outline" className="text-xs">
                100% Auto-classified
              </Badge>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">8</div>
            <p className="text-sm text-slate-600">Performance tracking</p>
            <div className="mt-2">
              <Badge variant="outline" className="text-xs">
                85% Auto-classified
              </Badge>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Marketing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600">15</div>
            <p className="text-sm text-slate-600">Advertising cookies</p>
            <div className="mt-2">
              <Badge variant="outline" className="text-xs">
                92% Auto-classified
              </Badge>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Functional</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-600">5</div>
            <p className="text-sm text-slate-600">Feature enhancement</p>
            <div className="mt-2">
              <Badge variant="outline" className="text-xs">
                78% Auto-classified
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Cookie Inventory</CardTitle>
          <CardDescription>All discovered cookies across your domains</CardDescription>
        </CardHeader>
        <CardContent>
          <CookieDictionary useForDashboard={true} />
        </CardContent>
      </Card>
    </div>
  );
}
