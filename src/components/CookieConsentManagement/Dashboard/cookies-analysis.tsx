import { useEffect, useState } from 'react';
import { Badge } from '../../../@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Progress } from '../../../@/components/ui/Common/Elements/ProgressBar/ProgressBar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../@/components/ui/tabs';

import {
  CheckCircle,
  CircleCheck,
  Eye,
  Monitor,
  MousePointer,
  Shield,
  Smartphone,
  Tablet,
  TrendingUp,
  Users,
  XCircle,
  Zap,
} from 'lucide-react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store';
import { StatsDivData } from '../../../types/cookie-consent-management';
import { get_consent_rate_data } from '../../common/services/cookie-consent-management';

interface AnalyticsProp {
  selectedDomainId: number;
}

export function Analytics({ selectedDomainId }: AnalyticsProp) {
  const keyMetrics = [
    {
      title: 'Total Consents',
      value: '2.4M',
      change: '+15.3% from last month',
      trend: 'positive',
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Opt-in Rate',
      value: '82.1%',
      change: '****% improvement',
      trend: 'positive',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      showProgress: true,
      progressValue: 82.1,
    },
    {
      title: 'Active Consents',
      value: '1.97M',
      change: '430K expired this month',
      trend: 'neutral',
      icon: CircleCheck,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Avg Load Time',
      value: '68ms',
      change: '-12ms improvement',
      trend: 'positive',
      icon: Zap,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Banner Interactions',
      value: '89.7%',
      change: '',
      trend: 'positive',
      icon: MousePointer,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      showProgress: true,
      progressValue: 89.7,
    },
    {
      title: 'Compliance Score',
      value: '97.8%',
      change: '+1.2% this quarter',
      trend: 'positive',
      icon: Shield,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      showProgress: true,
      progressValue: 97.8,
    },
  ];

  const realTimeMetrics = [
    { label: 'Consents/Hour', value: '1,247', trend: '+8%' },
    { label: 'Active Banners', value: '2,842', trend: 'stable' },
    { label: 'Avg Response Time', value: '45ms', trend: '-5ms' },
  ];

  const consentTrends = [
    { date: 'Jan 2024', accept: 78.5, decline: 12.8, customize: 8.7 },
    { date: 'Feb 2024', accept: 80.1, decline: 11.9, customize: 8.0 },
    { date: 'Mar 2024', accept: 82.3, decline: 10.5, customize: 7.2 },
    { date: 'Apr 2024', accept: 81.7, decline: 11.2, customize: 7.1 },
    { date: 'May 2024', accept: 83.4, decline: 9.8, customize: 6.8 },
  ];

  const categoryPerformance = [
    { category: 'Necessary', consent: 100, required: true, color: 'bg-gray-500' },
    { category: 'Analytics', consent: 91.5, required: false, color: 'bg-blue-500' },
    { category: 'Marketing', consent: 74.2, required: false, color: 'bg-green-500' },
    { category: 'Preferences', consent: 87.8, required: false, color: 'bg-purple-500' },
    { category: 'Social Media', consent: 68.9, required: false, color: 'bg-pink-500' },
  ];

  const geoPerformance = [
    { region: 'Europe (GDPR)', consents: 890000, rate: 89.2, compliance: 99.1, flag: '🇪🇺' },
    { region: 'North America', consents: 650000, rate: 78.4, compliance: 96.8, flag: '🇺🇸' },
    { region: 'California (CCPA)', consents: 280000, rate: 85.7, compliance: 98.2, flag: '🏴' },
    { region: 'Asia Pacific', consents: 420000, rate: 71.3, compliance: 94.5, flag: '🌏' },
    { region: 'Latin America', consents: 180000, rate: 76.1, compliance: 91.8, flag: '🌎' },
  ];

  const deviceBreakdown = [
    { device: 'Mobile', percentage: 68.4, icon: Smartphone },
    { device: 'Desktop', percentage: 26.8, icon: Monitor },
    { device: 'Tablet', percentage: 4.8, icon: Tablet },
  ];

  const performanceMetrics = [
    { metric: 'Time to Interactive', value: '124ms', target: '<200ms', status: 'good' },
    { metric: 'Bundle Size', value: '38KB', target: '<50KB', status: 'good' },
    { metric: 'Core Web Vitals Impact', value: '0.02s', target: '<0.1s', status: 'excellent' },
    { metric: 'CDN Response Time', value: '28ms', target: '<50ms', status: 'excellent' },
  ];

  const complianceStatus = [
    { framework: 'GDPR', score: 99.1, status: 'Compliant', lastAudit: '2024-01-10' },
    { framework: 'CCPA/CPRA', score: 98.2, status: 'Compliant', lastAudit: '2024-01-08' },
    { framework: 'LGPD', score: 96.7, status: 'Compliant', lastAudit: '2024-01-12' },
    { framework: 'PIPEDA', score: 94.3, status: 'Review Required', lastAudit: '2024-01-05' },
  ];
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  // const [isLoadingStats, setIsLoadingStats] = useState<boolean>(true);
  const [rateData, setRateData] = useState<StatsDivData>();

  useEffect(() => {
    const fetchConsentRateCounts = async () => {
      // setIsLoadingStats(true);
      try {
        const responseData = await get_consent_rate_data(customer_id, selectedDomainId);
        setRateData(responseData.result?.data[0]);
      } catch (error) {
        console.error(error);
      } finally {
        // setIsLoadingStats(false);
      }
    };

    if (customer_id !== undefined) {
      fetchConsentRateCounts();
    }
  }, [customer_id, selectedDomainId]);

  return (
    <div className="space-y-6 pb-6">
      {/* Header with Real-time Controls */}
      {/* <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Cookie Consent Analytics</h1>
          <p className="text-gray-600">Real-time consent performance and compliance monitoring</p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2 rounded-lg bg-green-50 px-3 py-2">
            <div className="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
            <span className="text-sm font-medium text-green-700">Live Data</span>
          </div>
          <Select defaultValue="24h">
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 3 months</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Auto-refresh: ON
          </Button>
          <Button className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div> */}

      {/* Real-time Metrics Bar */}
      <div className="mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3">
        {realTimeMetrics.map((metric, index) => (
          <Card key={index} className="border-0 ">
            <CardContent className="p-4 text-center">
              <div className="text-sm text-gray-600">{metric.label}</div>
              <div className="text-2xl font-bold">{metric.value}</div>
              <div
                className={`mt-1 text-xs ${
                  metric.trend.includes('+')
                    ? 'text-green-600'
                    : metric.trend.includes('-') && metric.trend.includes('ms')
                      ? 'text-red-600'
                      : 'text-blue-600'
                }`}
              >
                {metric.trend}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {keyMetrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <Card key={index} className="relative overflow-hidden">
              <CardContent className="p-6">
                <div className="mb-4 flex items-center justify-between">
                  <div className={`rounded-lg p-3 ${metric.bgColor}`}>
                    <Icon className={`h-6 w-6 ${metric.color}`} />
                  </div>
                  <Badge variant="outline" className="text-xs">
                    Live
                  </Badge>
                </div>
                <div>
                  <p className="mb-1 text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="mb-2 text-3xl font-bold">{metric.value}</p>
                  {metric.change && (
                    <p
                      className={`text-xs ${
                        metric.trend === 'positive'
                          ? 'text-green-600'
                          : metric.trend === 'negative'
                            ? 'text-red-600'
                            : 'text-gray-600'
                      }`}
                    >
                      {metric.change}
                    </p>
                  )}
                  {metric.showProgress && (
                    <div className="mt-3">
                      <Progress value={metric.progressValue} className="h-2" />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="consent">Consent Analysis</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="geography">Geography</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Consent Trends Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Consent Trends (Last 5 Months)</CardTitle>
                <CardDescription>User interaction patterns and consent decisions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {consentTrends.map((trend, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{trend.date}</span>
                        <div className="flex space-x-4 text-xs">
                          <span className="text-green-600">Accept: {trend.accept}%</span>
                          <span className="text-red-600">Decline: {trend.decline}%</span>
                          <span className="text-blue-600">Customize: {trend.customize}%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-gray-200">
                        <div className="flex h-2 overflow-hidden rounded-full">
                          <div className="bg-green-500" style={{ width: `${trend.accept}%` }}></div>
                          <div className="bg-red-500" style={{ width: `${trend.decline}%` }}></div>
                          <div
                            className="bg-blue-500"
                            style={{ width: `${trend.customize}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Device Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Device Distribution</CardTitle>
                <CardDescription>Consent patterns across device types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {deviceBreakdown.map((device, index) => {
                    const Icon = device.icon;
                    return (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="rounded-lg bg-gray-100 p-2">
                            <Icon className="h-5 w-5 text-gray-600" />
                          </div>
                          <span className="font-medium">{device.device}</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="h-2 w-32 rounded-full bg-gray-200">
                            <div
                              className="h-2 rounded-full bg-blue-500"
                              style={{ width: `${device.percentage}%` }}
                            ></div>
                          </div>
                          <span className="w-12 text-sm font-bold">{device.percentage}%</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Category Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Consent Categories Performance</CardTitle>
              <CardDescription>Breakdown of consent rates by data usage purpose</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
                {categoryPerformance.map((category, index) => (
                  <div key={index} className="rounded-lg border p-4 text-center">
                    <div className="mb-2 flex items-center justify-center">
                      <div className={`h-4 w-4 rounded-full ${category.color} mr-2`}></div>
                      <span className="font-medium">{category.category}</span>
                      {category.required && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          Required
                        </Badge>
                      )}
                    </div>
                    <div className="mb-1 text-2xl font-bold">{category.consent}%</div>
                    <Progress value={category.consent} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="consent" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Consent Funnel Analysis</CardTitle>
                <CardDescription>User journey through consent process</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between rounded-lg bg-blue-50 p-4">
                    <div className="flex items-center space-x-3">
                      <Eye className="h-5 w-5 text-blue-600" />
                      <span className="font-medium">Total Users</span>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold">{rateData?.interaction_count ?? '-'}</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between rounded-lg bg-green-50 p-4">
                    <div className="flex items-center space-x-3">
                      <MousePointer className="h-5 w-5 text-green-600" />
                      <span className="font-medium">Interactions</span>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold">2,554,923</div>
                      <div className="text-sm text-gray-600">89.7%</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between rounded-lg bg-emerald-50 p-4">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-emerald-600" />
                      <span className="font-medium">Consents Given</span>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold">2,396,247</div>
                      <div className="text-sm text-gray-600">84.2%</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between rounded-lg bg-red-50 p-4">
                    <div className="flex items-center space-x-3">
                      <XCircle className="h-5 w-5 text-red-600" />
                      <span className="font-medium">Rejections</span>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold">158,676</div>
                      <div className="text-sm text-gray-600">5.6%</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Interaction Insights</CardTitle>
                <CardDescription>User behavior patterns</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Avg. Time to Decision</span>
                    <span className="font-bold">3.2s</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Banner Abandonment</span>
                    <span className="font-bold">10.3%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Return Visitors</span>
                    <span className="font-bold">68.7%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Mobile Preference</span>
                    <span className="font-bold">71.2%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Peak Hours</span>
                    <span className="font-bold">2-4 PM</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-1">
            <Card>
              <CardHeader>
                <CardTitle>Global CDN Performance</CardTitle>
                <CardDescription>Response times by region</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2">
                    <span className="text-sm">🇺🇸 North America</span>
                    <span className="font-medium text-green-600">24ms</span>
                  </div>
                  <div className="flex items-center justify-between p-2">
                    <span className="text-sm">🇪🇺 Europe</span>
                    <span className="font-medium text-green-600">28ms</span>
                  </div>
                  <div className="flex items-center justify-between p-2">
                    <span className="text-sm">🌏 Asia Pacific</span>
                    <span className="font-medium text-yellow-600">45ms</span>
                  </div>
                  <div className="flex items-center justify-between p-2">
                    <span className="text-sm">🌎 Latin America</span>
                    <span className="font-medium text-green-600">32ms</span>
                  </div>
                  <div className="flex items-center justify-between p-2">
                    <span className="text-sm">🌍 Africa</span>
                    <span className="font-medium text-yellow-600">58ms</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Regulatory Compliance Status</CardTitle>
              <CardDescription>
                Comprehensive compliance monitoring across frameworks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {complianceStatus.map((compliance, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-4"
                  >
                    <div className="flex items-center space-x-4">
                      <Shield className="h-8 w-8 text-blue-600" />
                      <div>
                        <h4 className="font-medium">{compliance.framework}</h4>
                        <p className="text-sm text-gray-600">Last audit: {compliance.lastAudit}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">{compliance.score}%</div>
                      <Badge variant={compliance.status === 'Compliant' ? 'default' : 'secondary'}>
                        {compliance.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="geography" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Geographic Performance Analysis</CardTitle>
              <CardDescription>Consent rates and compliance by region</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {geoPerformance.map((region, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">{region.flag}</div>
                      <div>
                        <h4 className="font-medium">{region.region}</h4>
                        <p className="text-sm text-gray-600">
                          {region.consents.toLocaleString()} consents
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">{region.rate}%</div>
                        <div className="text-xs text-gray-600">Consent Rate</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">{region.compliance}%</div>
                        <div className="text-xs text-gray-600">Compliance</div>
                      </div>
                      <Badge variant={region.compliance > 95 ? 'default' : 'secondary'}>
                        {region.compliance > 95 ? 'Excellent' : 'Good'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
